<?php

namespace App\Exports;

use App\Models\PostalVehicle;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PostalVehicleExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return PostalVehicle::with(['garage', 'serviceProvider'])->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Rendszám',
            'Gyártmány',
            'Modell',
            'Alvázszám',
            'Kategória',
            'Szerződés besorolás',
            'Gyártási év',
            'Üzemanyag',
            'Hatósági vizsga érvényessége',
            '<PERSON><PERSON><PERSON><PERSON><PERSON> neve',
            '<PERSON>ar<PERSON>zs email',
            '<PERSON><PERSON>vi<PERSON> neve',
            '<PERSON><PERSON><PERSON><PERSON> munkatárs neve',
            'Műszaki munkatárs email',
            'Műszaki menedzser neve',
            'Műszaki menedzser email',
            'Létrehozva',
            'Módosítva',
        ];
    }

    /**
     * @param mixed $vehicle
     * @return array
     */
    public function map($vehicle): array
    {
        return [
            $vehicle->license_plate,
            $vehicle->manufacturer ?: $vehicle->type, // Fallback to old type field if manufacturer is empty
            $vehicle->model,
            $vehicle->chassis_number,
            $vehicle->category,
            $vehicle->contract_classification,
            $vehicle->manufacturing_year,
            $vehicle->fuel_type,
            $vehicle->authority_inspection_valid_until?->format('Y-m-d'),
            $vehicle->garage?->name,
            $vehicle->garage_central_email,
            $vehicle->serviceProvider?->name,
            $vehicle->technical_staff_name,
            $vehicle->technical_staff_email,
            $vehicle->technical_manager_name,
            $vehicle->technical_manager_email,
            $vehicle->created_at?->format('Y-m-d H:i:s'),
            $vehicle->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }
}
