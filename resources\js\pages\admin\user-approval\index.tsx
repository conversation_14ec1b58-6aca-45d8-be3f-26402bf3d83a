import { Head, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Building2, CheckCircle, Eye } from 'lucide-react';
import { useState } from 'react';

import { UserApprovalModal } from '@/components/admin/UserApprovalModal';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';

type User = {
    id: number;
    name: string;
    email: string;
    company_type: 'posta' | 'partner';
    company_name?: string;
    tax_number?: string;
    position?: string;
    phone?: string;
    created_at: string;
    contact?: {
        id: number;
        name: string;
        username: string;
    };
    org_unit?: {
        id: number;
        name: string;
    };
    garage?: {
        id: number;
        name: string;
    };
};

type PageProps = {
    pendingUsers: User[];
    canManageUsers: boolean;
    canManagePostaUsers: boolean;
};

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Felhasználó jóváhagyás', href: '/admin/user-approval' }];

export default function UserApprovalIndex() {
    const { pendingUsers, canManageUsers, canManagePostaUsers } = usePage<PageProps>().props;
    const [selectedUser, setSelectedUser] = useState<User | null>(null);
    const [modalOpen, setModalOpen] = useState(false);

    const handleViewDetails = (user: User) => {
        setSelectedUser(user);
        setModalOpen(true);
    };

    const handleCloseModal = () => {
        setSelectedUser(null);
        setModalOpen(false);
    };

    const getCompanyTypeBadge = (companyType: 'posta' | 'partner') => {
        if (companyType === 'posta') {
            return (
                <Badge variant="default" className="bg-blue-100 text-blue-800">
                    Magyar Posta
                </Badge>
            );
        }
        return (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
                Partner
            </Badge>
        );
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('hu-HU', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const columns: ColumnDef<User>[] = [
        {
            header: 'Név',
            accessorKey: 'name',
            meta: { align: 'left', sortable: true },
        },
        {
            header: 'Email',
            accessorKey: 'email',
            meta: { align: 'left', sortable: true },
        },
        {
            header: 'Típus',
            accessorKey: 'company_type',
            cell: ({ row }) => getCompanyTypeBadge(row.original.company_type),
            meta: { align: 'left', sortable: true },
        },
        {
            header: 'Cég/Szervezet',
            id: 'organization',
            cell: ({ row }) => {
                const user = row.original;
                if (user.company_type === 'posta') {
                    return user.org_unit?.name || '-';
                }
                return user.company_name || '-';
            },
            meta: { align: 'left', sortable: false },
        },
        {
            header: 'Regisztráció',
            accessorKey: 'created_at',
            cell: ({ row }) => formatDate(row.original.created_at),
            meta: { align: 'left', sortable: true },
        },
        {
            header: '',
            id: 'actions',
            cell: ({ row }) => {
                const user = row.original;
                const canManage = (user.company_type === 'posta' && canManagePostaUsers) || (user.company_type === 'partner' && canManageUsers);

                if (!canManage) {
                    return <span className="text-sm text-muted-foreground">Nincs jogosultság</span>;
                }

                return (
                    <Button variant="outline" size="sm" onClick={() => handleViewDetails(user)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Részletek
                    </Button>
                );
            },
            meta: { align: 'right', sortable: false },
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Felhasználó jóváhagyás" />

            <div className="h-full space-y-6 p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold">Felhasználó jóváhagyás</h1>
                        <p className="mt-2 text-muted-foreground">Jóváhagyásra váró regisztrációk kezelése</p>
                    </div>

                    <div className="flex items-center gap-4">
                        <div className="text-sm text-muted-foreground">
                            <strong>{pendingUsers.length}</strong> jóváhagyásra váró felhasználó
                        </div>
                    </div>
                </div>

                {/* Jogosultság információ */}
                <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
                    <div className="flex items-center gap-2 text-blue-800">
                        <Building2 className="h-5 w-5" />
                        <span className="font-medium">Jogosultságai:</span>
                    </div>
                    <div className="mt-2 flex gap-4">
                        {canManagePostaUsers && (
                            <Badge variant="default" className="bg-blue-100 text-blue-800">
                                Magyar Posta felhasználók
                            </Badge>
                        )}
                        {canManageUsers && (
                            <Badge variant="secondary" className="bg-green-100 text-green-800">
                                Partner felhasználók
                            </Badge>
                        )}
                        {!canManageUsers && !canManagePostaUsers && (
                            <span className="text-red-600">Nincs jogosultsága felhasználók jóváhagyásához</span>
                        )}
                    </div>
                </div>

                {/* Felhasználók táblázata */}
                {pendingUsers.length > 0 ? (
                    <DataTable columns={columns} data={pendingUsers} pageSize={15} />
                ) : (
                    <div className="py-12 text-center">
                        <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                        <h3 className="mb-2 text-lg font-medium text-gray-900">Nincs jóváhagyásra váró felhasználó</h3>
                        <p className="text-gray-500">Jelenleg minden regisztráció el van bírálva.</p>
                    </div>
                )}
            </div>

            {/* Részletek modal */}
            {selectedUser && (
                <UserApprovalModal
                    user={selectedUser}
                    open={modalOpen}
                    onClose={handleCloseModal}
                    canManageUsers={canManageUsers}
                    canManagePostaUsers={canManagePostaUsers}
                />
            )}
        </AppLayout>
    );
}
