<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Garage extends Model
{
    protected $fillable = [
        'name',
        'postal_code',
        'city',
        'address',
    ];

    /**
     * Get the contacts for the garage.
     */
    public function contacts(): Has<PERSON>any
    {
        return $this->hasMany(Contact::class);
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        return "{$this->postal_code} {$this->city}, {$this->address}";
    }
}
