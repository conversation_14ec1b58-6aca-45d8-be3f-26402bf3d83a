<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\PermissionRegistrar;

class ContactPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        // Kapcsolattartók jogosultságok létrehozása
        $permission = Permission::firstOrCreate(
            ['name' => 'import_postal_contacts'],
            [
                'display_name' => 'Postai kapcsolattartók importálása',
                'description' => 'Kapcsolattartók Excel/CSV fájlból történő tömeges importálása',
                'category' => 'data_management',
                'is_dangerous' => true,
                'sort_order' => 301
            ]
        );

        // Jogosultság hozzárendelése a megfelelő szerepkörökhöz
        $roles = Role::whereIn('name', [
            'SystemAdmin',
            '<PERSON>aAdmin',
            '<PERSON><PERSON><PERSON><PERSON> menedzser',
            '<PERSON><PERSON><PERSON><PERSON><PERSON>vezető'
        ])->get();

        foreach ($roles as $role) {
            $role->givePermissionTo($permission);
        }

        $this->command->info('Kapcsolattartó import jogosultság sikeresen létrehozva és hozzárendelve.');
    }
}