import PermissionForm from '@/components/admin/PermissionForm';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { getCategoryInfo } from '@/utils/permissions';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { AlertTriangle, Download, Search } from 'lucide-react';
import { useState } from 'react';
import * as XLSX from 'xlsx';

// Define permission type
interface Permission {
    id: number;
    name: string;
    display_name?: string;
    description?: string;
    category: string;
    is_dangerous: boolean;
    sort_order: number;
    roles?: { name: string }[];
}

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Jogosultságok', href: '/permissions' }];

export default function PermissionIndex() {
    const { permissions } = usePage<{
        permissions: Permission[];
    }>().props;

    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<Permission | null>(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [deleteTarget, setDeleteTarget] = useState<Permission | null>(null);

    const columns: ColumnDef<Permission>[] = [
        {
            header: 'Technikai név',
            accessorKey: 'name',
            meta: { align: 'left', sortable: true },
            cell: ({ row }) => <code className="rounded bg-gray-100 px-2 py-1 text-xs">{row.original.name}</code>,
        },
        {
            header: 'Magyar név',
            accessorKey: 'display_name',
            meta: { align: 'left', sortable: true },
            cell: ({ row }) => (
                <div className="flex items-center gap-2">
                    <span>{row.original.display_name || row.original.name}</span>
                    {!!row.original.is_dangerous && <AlertTriangle className="h-4 w-4 text-amber-500" />}
                </div>
            ),
        },
        {
            header: 'Kategória',
            accessorKey: 'category',
            meta: { align: 'left', sortable: true },
            cell: ({ row }) => {
                const categoryInfo = getCategoryInfo(row.original.category);
                return (
                    <span className="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
                        {categoryInfo?.name || row.original.category}
                    </span>
                );
            },
        },
        {
            header: 'Leírás',
            accessorKey: 'description',
            meta: { align: 'left', sortable: false },
            cell: ({ row }) => <span className="block max-w-xs truncate text-sm text-gray-600">{row.original.description || '-'}</span>,
        },
        {
            header: 'Szerepkörök',
            accessorKey: 'roles',
            accessorFn: (row) => row.roles?.map((r) => r.name) || [],
            meta: { align: 'left', sortable: false },
            cell: ({ row }) => (
                <div className="flex flex-wrap gap-1">
                    {row.original.roles?.slice(0, 2).map((role) => (
                        <span
                            key={role.name}
                            className="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800"
                        >
                            {role.name}
                        </span>
                    ))}
                    {(row.original.roles?.length || 0) > 2 && (
                        <span className="text-xs text-gray-500">+{(row.original.roles?.length || 0) - 2} további</span>
                    )}
                </div>
            ),
        },
    ];

    const handleEdit = (permission: Permission) => {
        setEditing(permission);
        setOpen(true);
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const confirmDelete = (permission: Permission) => {
        setDeleteTarget(permission);
    };

    const performDelete = () => {
        if (deleteTarget) {
            router.delete(`/admin/permissions/${deleteTarget.id}`);
            setDeleteTarget(null);
        }
    };

    const handleExportExcel = (data: Permission[]) => {
        const exportData = data.map((permission) => ({
            Név: permission.name,
            Leírás: permission.description ?? '',
            Szerepkörök: permission.roles?.map((r) => r.name).join(', ') ?? '',
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Jogosultságok');
        XLSX.writeFile(workbook, 'jogosultsagok.xlsx');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Jogosultságok" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {' '}
                <div className="flex items-center justify-between">
                    <Button onClick={() => handleExportExcel(permissions)} variant="outline" size="sm" className="flex items-center gap-2">
                        <Download className="h-4 w-4" /> Excel export
                    </Button>
                    <div className="flex items-center gap-3">
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Keresés..."
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                className="w-64 border-gray-200 pl-10 shadow-sm focus:border-blue-500 focus:ring-blue-500/20"
                            />
                        </div>
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                    + Új jogosultság
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="max-w-2xl"
                                onInteractOutside={(e) => {
                                    // Megakadályozzuk, hogy a dialog bezáródjon háttérkattintásra
                                    e.preventDefault();
                                }}
                            >
                                <PermissionForm permission={editing} onClose={() => setOpen(false)} />
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
                <DataTable
                    columns={columns}
                    data={permissions}
                    onEdit={handleEdit}
                    onDelete={confirmDelete}
                    globalFilter={globalFilter}
                    exportFileName="jogosultsagok"
                    pageSize={10}
                    showExportButton={false}
                />
            </div>

            <AlertDialog open={!!deleteTarget} onOpenChange={(open) => !open && setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Biztosan törölni szeretnéd ezt a jogosultságot?</AlertDialogTitle>
                        <p className="text-muted-foreground">{deleteTarget?.name}</p>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégsem</AlertDialogCancel>
                        <AlertDialogAction onClick={performDelete} className="bg-red-600 text-white hover:bg-red-700">
                            Törlés
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
