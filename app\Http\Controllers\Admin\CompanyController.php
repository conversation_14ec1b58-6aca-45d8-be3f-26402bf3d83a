<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use App\Models\Company;
use App\Http\Requests\CompanyRequest;
use Redirect;


class CompanyController extends Controller
{
    public function index()
    {
        return Inertia::render('admin/companies/index', [
            'companies' => Company::orderBy('name')->get(),
        ]);
    }

    public function store(CompanyRequest $request)
    {
        Company::create($request->validated());
        return Redirect::back()->with('success', 'Cég létrehozva.');
    }

    public function update(CompanyRequest $request, Company $company)
    {
        $company->update($request->validated());
        return Redirect::back()->with('success', 'Cég frissítve.');
    }

    public function destroy(Company $company)
    {
        $company->delete();
        return Redirect::back()->with('success', 'Cég törölve.');
    }
}
