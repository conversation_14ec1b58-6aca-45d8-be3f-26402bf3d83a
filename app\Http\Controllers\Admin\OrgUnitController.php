<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\OrgUnit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class OrgUnitController extends Controller
{
    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:org_units,name',
        ]);

        $orgUnit = OrgUnit::create($validated);

        return response()->json($orgUnit, 201);
    }
}
