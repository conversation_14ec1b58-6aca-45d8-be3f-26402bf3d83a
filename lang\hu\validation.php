<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted' => 'A :attribute mezőnek elfogadottnak kell lennie.',
    'accepted_if' => 'A :attribute mezőnek elfogadottnak kell lennie, ha :other :value.',
    'active_url' => 'A :attribute mezőnek érvényes URL-nek kell lennie.',
    'after' => 'A :attribute mezőnek egy :date utáni dátumnak kell lennie.',
    'after_or_equal' => 'A :attribute mezőnek egy :date utáni vagy egyenl<PERSON> dátumnak kell lennie.',
    'alpha' => 'A :attribute mező csak betűket tartalmazhat.',
    'alpha_dash' => 'A :attribute mező csak betűket, számokat, kötőjeleket és aláhúzásokat tartalmazhat.',
    'alpha_num' => 'A :attribute mező csak betűket és számokat tartalmazhat.',
    'any_of' => 'A :attribute mező érvénytelen.',
    'array' => 'A :attribute mezőnek tömbnek kell lennie.',
    'ascii' => 'A :attribute mező csak egyszerű alfanumerikus karaktereket és szimbólumokat tartalmazhat.',
    'before' => 'A :attribute mezőnek egy :date előtti dátumnak kell lennie.',
    'before_or_equal' => 'A :attribute mezőnek egy :date előtti vagy egyenlő dátumnak kell lennie.',
    'between' => [
        'array' => 'A :attribute mezőnek :min és :max közöttieknek kell lennie.',
        'file' => 'A :attribute mezőnek :min és :max kilobyte közöttieknek kell lennie.',
        'numeric' => 'A :attribute mezőnek :min és :max közöttieknek kell lennie.',
        'string' => 'A :attribute mezőnek :min és :max karakterből állónak kell lennie.',
    ],
    'boolean' => 'A :attribute mezőnek igaz vagy hamisnak kell lennie.',
    'can' => 'A :attribute mező érvénytelen értéket tartalmaz.',
    'confirmed' => 'A :attribute mező megerősítése nem egyezik.',
    'contains' => 'A :attribute mező hiányzik egy kötelező értéket.',
    'current_password' => 'A jelszó hibás.',
    'date' => 'A :attribute mezőnek érvényes dátumnak kell lennie.',
    'date_equals' => 'A :attribute mezőnek egy :date dátumnak kell lennie.',
    'date_format' => 'A :attribute mezőnek megfelelő formátumot kell tartalmaznia.',
    'decimal' => 'A :attribute mezőnek :decimal tizedesjegyet kell tartalmaznia.',
    'declined' => 'A :attribute mezőnek elutasítottnak kell lennie.',
    'declined_if' => 'A :attribute mezőnek elutasítottnak kell lennie, ha :other :value.',
    'different' => 'A :attribute mező és a :other mezőnek különbözőnek kell lennie.',
    'digits' => 'A :attribute mezőnek :digits számjegyet kell tartalmaznia.',
    'digits_between' => 'A :attribute mezőnek :min és :max számjegyet kell tartalmaznia.',
    'dimensions' => 'A :attribute mező érvénytelen kép méreteit tartalmaz.',
    'distinct' => 'A :attribute mezőnek egyedi értéket kell tartalmaznia.',
    'doesnt_end_with' => 'A :attribute mezőnek nem szabad egyik :values értékkel sem végződnie.',
    'doesnt_start_with' => 'A :attribute mezőnek nem szabad egyik :values értékkel sem kezdődnie.',
    'email' => 'A :attribute mezőnek érvényes email címnek kell lennie.',
    'ends_with' => 'A :attribute mezőnek végződnie kell egyik :values értékkel sem.',
    'enum' => 'A kiválasztott :attribute érvénytelen.',
    'exists' => 'A kiválasztott :attribute érvénytelen.',
    'extensions' => 'A :attribute mezőnek egyik :values kiterjesztésűnek kell lennie.',
    'file' => 'A :attribute mezőnek fájlnak kell lennie.',
    'filled' => 'The :attribute field must have a value.',
    'gt' => [
        'array' => 'The :attribute field must have more than :value items.',
        'file' => 'The :attribute field must be greater than :value kilobytes.',
        'numeric' => 'The :attribute field must be greater than :value.',
        'string' => 'The :attribute field must be greater than :value characters.',
    ],
    'gte' => [
        'array' => 'The :attribute field must have :value items or more.',
        'file' => 'The :attribute field must be greater than or equal to :value kilobytes.',
        'numeric' => 'The :attribute field must be greater than or equal to :value.',
        'string' => 'The :attribute field must be greater than or equal to :value characters.',
    ],
    'hex_color' => 'The :attribute field must be a valid hexadecimal color.',
    'image' => 'The :attribute field must be an image.',
    'in' => 'The selected :attribute is invalid.',
    'in_array' => 'The :attribute field must exist in :other.',
    'in_array_keys' => 'The :attribute field must contain at least one of the following keys: :values.',
    'integer' => 'The :attribute field must be an integer.',
    'ip' => 'The :attribute field must be a valid IP address.',
    'ipv4' => 'The :attribute field must be a valid IPv4 address.',
    'ipv6' => 'The :attribute field must be a valid IPv6 address.',
    'json' => 'The :attribute field must be a valid JSON string.',
    'list' => 'The :attribute field must be a list.',
    'lowercase' => 'The :attribute field must be lowercase.',
    'lt' => [
        'array' => 'The :attribute field must have less than :value items.',
        'file' => 'The :attribute field must be less than :value kilobytes.',
        'numeric' => 'The :attribute field must be less than :value.',
        'string' => 'The :attribute field must be less than :value characters.',
    ],
    'lte' => [
        'array' => 'The :attribute field must not have more than :value items.',
        'file' => 'The :attribute field must be less than or equal to :value kilobytes.',
        'numeric' => 'The :attribute field must be less than or equal to :value.',
        'string' => 'The :attribute field must be less than or equal to :value characters.',
    ],
    'mac_address' => 'The :attribute field must be a valid MAC address.',
    'max' => [
        'array' => 'The :attribute field must not have more than :max items.',
        'file' => 'The :attribute field must not be greater than :max kilobytes.',
        'numeric' => 'The :attribute field must not be greater than :max.',
        'string' => 'The :attribute field must not be greater than :max characters.',
    ],
    'max_digits' => 'The :attribute field must not have more than :max digits.',
    'mimes' => 'The :attribute field must be a file of type: :values.',
    'mimetypes' => 'The :attribute field must be a file of type: :values.',
    'min' => [
        'array' => 'The :attribute field must have at least :min items.',
        'file' => 'The :attribute field must be at least :min kilobytes.',
        'numeric' => 'The :attribute field must be at least :min.',
        'string' => 'The :attribute field must be at least :min characters.',
    ],
    'min_digits' => 'The :attribute field must have at least :min digits.',
    'missing' => 'The :attribute field must be missing.',
    'missing_if' => 'The :attribute field must be missing when :other is :value.',
    'missing_unless' => 'The :attribute field must be missing unless :other is :value.',
    'missing_with' => 'The :attribute field must be missing when :values is present.',
    'missing_with_all' => 'The :attribute field must be missing when :values are present.',
    'multiple_of' => 'The :attribute field must be a multiple of :value.',
    'not_in' => 'The selected :attribute is invalid.',
    'not_regex' => 'The :attribute field format is invalid.',
    'numeric' => 'The :attribute field must be a number.',
    'password' => [
        'letters' => 'The :attribute field must contain at least one letter.',
        'mixed' => 'The :attribute field must contain at least one uppercase and one lowercase letter.',
        'numbers' => 'The :attribute field must contain at least one number.',
        'symbols' => 'The :attribute field must contain at least one symbol.',
        'uncompromised' => 'The given :attribute has appeared in a data leak. Please choose a different :attribute.',
    ],
    'present' => 'The :attribute field must be present.',
    'present_if' => 'The :attribute field must be present when :other is :value.',
    'present_unless' => 'The :attribute field must be present unless :other is :value.',
    'present_with' => 'The :attribute field must be present when :values is present.',
    'present_with_all' => 'The :attribute field must be present when :values are present.',
    'prohibited' => 'The :attribute field is prohibited.',
    'prohibited_if' => 'The :attribute field is prohibited when :other is :value.',
    'prohibited_if_accepted' => 'The :attribute field is prohibited when :other is accepted.',
    'prohibited_if_declined' => 'The :attribute field is prohibited when :other is declined.',
    'prohibited_unless' => 'The :attribute field is prohibited unless :other is in :values.',
    'prohibits' => 'The :attribute field prohibits :other from being present.',
    'regex' => 'The :attribute field format is invalid.',
    'required' => 'The :attribute field is required.',
    'required_array_keys' => 'The :attribute field must contain entries for: :values.',
    'required_if' => 'The :attribute field is required when :other is :value.',
    'required_if_accepted' => 'The :attribute field is required when :other is accepted.',
    'required_if_declined' => 'The :attribute field is required when :other is declined.',
    'required_unless' => 'The :attribute field is required unless :other is in :values.',
    'required_with' => 'The :attribute field is required when :values is present.',
    'required_with_all' => 'The :attribute field is required when :values are present.',
    'required_without' => 'The :attribute field is required when :values is not present.',
    'required_without_all' => 'The :attribute field is required when none of :values are present.',
    'same' => 'The :attribute field must match :other.',
    'size' => [
        'array' => 'The :attribute field must contain :size items.',
        'file' => 'The :attribute field must be :size kilobytes.',
        'numeric' => 'The :attribute field must be :size.',
        'string' => 'The :attribute field must be :size characters.',
    ],
    'starts_with' => 'The :attribute field must start with one of the following: :values.',
    'string' => 'The :attribute field must be a string.',
    'timezone' => 'The :attribute field must be a valid timezone.',
    'unique' => 'The :attribute has already been taken.',
    'uploaded' => 'The :attribute failed to upload.',
    'uppercase' => 'The :attribute field must be uppercase.',
    'url' => 'The :attribute field must be a valid URL.',
    'ulid' => 'The :attribute field must be a valid ULID.',
    'uuid' => 'The :attribute field must be a valid UUID.',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [],

];
