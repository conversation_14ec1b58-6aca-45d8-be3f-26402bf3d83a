import { useState, useMemo } from 'react';
import {
  Search,
  ChevronRight,
  ChevronLeft,
  Shield,
  AlertTriangle,
  Info,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from '@/components/ui/tooltip';
import {
  getCategoryInfo,
  getPermissionsByCategory,
  searchPermissions,
  getPermissionDisplayName,
  getPermissionDescription,
  PERMISSION_CATEGORIES,
  type Permission,
} from '@/utils/permissions';

// dnd-kit
import {
  DndContext,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  PointerSensor,
  KeyboardSensor,
  useSensors,
  useSensor,
  useDroppable,
} from '@dnd-kit/core';
import {
  SortableContext,
  useSortable,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// ------------------------------------
// Props
// ------------------------------------

type Props = {
  availablePermissions: Permission[];
  selectedPermissionNames: string[];
  onChange: (permissionNames: string[]) => void;
  disabled?: boolean;
  dndEnabled?: boolean;
};

// ------------------------------------
// Component
// ------------------------------------

export function EnhancedPermissionSelector({
  availablePermissions,
  selectedPermissionNames,
  onChange,
  disabled = false,
  dndEnabled = false,
}: Props) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedAvailable, setSelectedAvailable] = useState<string[]>([]);
  const [selectedAssigned, setSelectedAssigned] = useState<string[]>([]);

  // dnd active item id
  const [activeId, setActiveId] = useState<string | null>(null);

  // Sensors (pointer + keyboard)
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: { distance: 5 },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const dndActive = dndEnabled && !disabled;

  // Helpers
  const allPermissionsMap = useMemo(
    () => new Map(availablePermissions.map((p) => [p.name, p])),
    [availablePermissions]
  );

  // Elérhető permission-ök szűrése
  const filteredAvailable = useMemo(() => {
    let permissions = availablePermissions.filter(
      (p) => !selectedPermissionNames.includes(p.name)
    );

    if (searchQuery) {
      permissions = searchPermissions(permissions, searchQuery);
    }

    if (selectedCategory !== 'all') {
      permissions = getPermissionsByCategory(permissions, selectedCategory);
    } else {
      permissions = permissions.sort((a, b) => a.sort_order - b.sort_order);
    }

    return permissions;
  }, [availablePermissions, selectedPermissionNames, searchQuery, selectedCategory]);

  // Kiválasztott (assigned) permission-ök
  const assignedPermissions = useMemo(() => {
    return availablePermissions
      .filter((p) => selectedPermissionNames.includes(p.name))
      .sort((a, b) => a.sort_order - b.sort_order);
  }, [availablePermissions, selectedPermissionNames]);

  // Toggle helpers for multiselect boxes
  const toggleAvailable = (name: string) =>
    setSelectedAvailable((prev) =>
      prev.includes(name) ? prev.filter((p) => p !== name) : [...prev, name]
    );

  const toggleAssigned = (name: string) =>
    setSelectedAssigned((prev) =>
      prev.includes(name) ? prev.filter((p) => p !== name) : [...prev, name]
    );

  // CRUD actions
  const addPermissions = () => {
    const newSelected = [...selectedPermissionNames, ...selectedAvailable];
    onChange(Array.from(new Set(newSelected)));
    setSelectedAvailable([]);
  };

  const removePermissions = () => {
    const newSelected = selectedPermissionNames.filter(
      (p) => !selectedAssigned.includes(p)
    );
    onChange(newSelected);
    setSelectedAssigned([]);
  };

  const addAll = () => {
    const toAdd = filteredAvailable.map((p) => p.name);
    onChange(Array.from(new Set([...selectedPermissionNames, ...toAdd])));
  };

  const removeAll = () => {
    onChange([]);
    setSelectedAssigned([]);
  };

  // ------------------------------------
  // dnd-kit logic
  // ------------------------------------

  const { setNodeRef: setAvailableDropRef } = useDroppable({ id: 'available-container' });
  const { setNodeRef: setAssignedDropRef } = useDroppable({ id: 'assigned-container' });

  const handleDragStart = (event: DragStartEvent) => { if (!dndActive) return;
    setActiveId(String(event.active.id));
  };

  const handleDragEnd = (event: DragEndEvent) => { if (!dndActive) return;
    const { active, over } = event;
    setActiveId(null);
    if (!over) return;

    const activeName = String(active.id);
    const overId = String(over.id);

    const isActiveAssigned = selectedPermissionNames.includes(activeName);
    const isOverAssigned =
      overId === 'assigned-container' || selectedPermissionNames.includes(overId);
    const isOverAvailable =
      overId === 'available-container' || filteredAvailable.some((p) => p.name === overId);

    // 1) Available -> Assigned
    if (!isActiveAssigned && isOverAssigned) {
      const assigned = [...selectedPermissionNames];
      if (selectedPermissionNames.includes(overId)) {
        const insertIndex = assigned.indexOf(overId);
        assigned.splice(insertIndex, 0, activeName);
      } else {
        assigned.push(activeName);
      }
      onChange(Array.from(new Set(assigned)));
      return;
    }

    // 2) Assigned -> Available
    if (isActiveAssigned && isOverAvailable) {
      onChange(selectedPermissionNames.filter((n) => n !== activeName));
      return;
    }

    // 3) Reorder within Assigned
    if (isActiveAssigned && isOverAssigned) {
      const oldIndex = selectedPermissionNames.indexOf(activeName);
      const newIndex = selectedPermissionNames.indexOf(overId);
      if (newIndex !== -1 && oldIndex !== newIndex) {
        onChange(arrayMove(selectedPermissionNames, oldIndex, newIndex));
      }
    }
  };

  // Keyboard-only selection (in addition to dnd-kit keyboard sensor)
  const handleItemKeyDown = (
    e: React.KeyboardEvent<HTMLDivElement>,
    name: string,
    list: 'available' | 'assigned'
  ) => {
    const isAvail = list === 'available';

    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault();
      (isAvail ? toggleAvailable : toggleAssigned)(name);
    }

    if (e.key === 'ArrowRight' && isAvail) {
      e.preventDefault();
      if (!selectedPermissionNames.includes(name)) {
        onChange([...selectedPermissionNames, name]);
      }
    }

    if (e.key === 'ArrowLeft' && !isAvail) {
      e.preventDefault();
      onChange(selectedPermissionNames.filter((n) => n !== name));
    }
  };

  // ------------------------------------
  // Sortable Permission Item
  // ------------------------------------

  const SortablePermissionItem = ({
    permission,
    isSelected,
    onToggle,
    list,
    index,
  }: {
    permission: Permission;
    isSelected: boolean;
    onToggle: (name: string) => void;
    list: 'available' | 'assigned';
    index: number;
  }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } =
      useSortable({ id: permission.name, disabled: !dndActive });

    const style: React.CSSProperties = {
      transform: CSS.Transform.toString(transform),
      transition,
      opacity: isDragging ? 0.5 : undefined,
    };

    return (
      <div
        ref={setNodeRef}
        style={style}
        aria-selected={isSelected}
        data-name={permission.name}
        data-index={index}
        data-list={list}
        onKeyDown={(e) => handleItemKeyDown(e, permission.name, list)}
        className={`p-3 border rounded-lg transition-colors cursor-pointer select-none
          ${isSelected ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'}
          focus:outline-none focus:ring-2 focus:ring-blue-400`}
        onClick={() => onToggle(permission.name)}
        {...attributes}
        {...listeners}
      >
        <div className="flex items-start gap-3">
          <Checkbox
            checked={isSelected}
            onCheckedChange={() => onToggle(permission.name)}
            disabled={disabled}
            onClick={(e) => e.stopPropagation()}
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-sm">
                {getPermissionDisplayName(permission)}
              </span>
              {!!permission.is_dangerous && (
                <AlertTriangle className="h-4 w-4 text-amber-500" />
              )}
            </div>
            <p className="text-xs text-gray-600 mb-2">
              {getPermissionDescription(permission)}
            </p>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                {getCategoryInfo(permission.category)?.name || 'Általános'}
              </Badge>
              <code className="text-xs text-gray-500 bg-gray-100 px-1 rounded">
                {permission.name}
              </code>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Active overlay item while dragging
  const activePermission = activeId ? allPermissionsMap.get(activeId) : undefined;

  return (
    <TooltipProvider delayDuration={150}>
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Shield className="h-4 w-4" />
          <span>Jogosultságok hozzárendelése</span>
          <Info className="h-4 w-4" />
        </div>

        {/* Keresés és szűrés */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Keresés jogosultságok között..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              disabled={disabled}
            />
          </div>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm"
            disabled={disabled}
          >
            <option value="all">Minden kategória</option>
            {PERMISSION_CATEGORIES.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>

        {/* Dual List (with custom grid) */}
        <DndContext
          sensors={sensors}
          onDragStart={handleDragStart}
          onDragEnd={handleDragEnd}
        >
          <div className="grid grid-cols-1 xl:grid-cols-[minmax(0,2fr)_auto_minmax(0,2fr)] gap-4">
            {/* Available */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium">
                  Elérhető jogosultságok ({filteredAvailable.length})
                <span className="ml-2 text-xs text-gray-500">Kijelölve: {selectedAvailable.length}</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={addAll}
                      disabled={disabled || filteredAvailable.length === 0}
                      className="cursor-pointer"
                    >
                      Összes hozzáadása
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mindet átrakja jobbra</TooltipContent>
                </Tooltip>
              </div>

              <div
                ref={setAvailableDropRef}
                className="border rounded-lg p-2 h-96 overflow-y-auto space-y-2"
              >
                <SortableContext
                  items={filteredAvailable.map((p) => p.name)}
                  strategy={verticalListSortingStrategy}
                >
                  {filteredAvailable.map((permission, i) => (
                    <SortablePermissionItem
                      key={permission.id}
                      permission={permission}
                      isSelected={selectedAvailable.includes(permission.name)}
                      onToggle={toggleAvailable}
                      list="available"
                      index={i}
                    />
                  ))}
                </SortableContext>
                {filteredAvailable.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nincs elérhető jogosultság</p>
                  </div>
                )}
              </div>
            </div>

            {/* Control buttons */}
            <div className="flex xl:flex-col items-center justify-center gap-2">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={addPermissions}
                    disabled={disabled || selectedAvailable.length === 0}
                    className="h-8 w-8 p-0 shrink-0 cursor-pointer hover:bg-gray-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Kijelöltek hozzáadása ({selectedAvailable.length})</TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={removePermissions}
                    disabled={disabled || selectedAssigned.length === 0}
                    className="h-8 w-8 p-0 shrink-0 cursor-pointer hover:bg-gray-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Kijelöltek eltávolítása ({selectedAssigned.length})</TooltipContent>
              </Tooltip>
            </div>

            {/* Assigned */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium">
                  Hozzárendelt jogosultságok ({assignedPermissions.length})
                <span className="ml-2 text-xs text-gray-500">Kijelölve: {selectedAssigned.length}</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={removeAll}
                      disabled={disabled || assignedPermissions.length === 0}
                      className="cursor-pointer"
                    >
                      Összes eltávolítása
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>Mindet visszarakja balra</TooltipContent>
                </Tooltip>
              </div>

              <div
                ref={setAssignedDropRef}
                className="border rounded-lg p-2 h-96 overflow-y-auto space-y-2"
              >
                <SortableContext
                  items={assignedPermissions.map((p) => p.name)}
                  strategy={verticalListSortingStrategy}
                >
                  {assignedPermissions.map((permission, i) => (
                    <SortablePermissionItem
                      key={permission.id}
                      permission={permission}
                      isSelected={selectedAssigned.includes(permission.name)}
                      onToggle={toggleAssigned}
                      list="assigned"
                      index={i}
                    />
                  ))}
                </SortableContext>
                {assignedPermissions.length === 0 && (
                  <div className="text-center text-gray-500 py-8">
                    <Shield className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">Nincs hozzárendelt jogosultság</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Drag overlay */}
          <DragOverlay>
            {activePermission ? (
              <div className="opacity-90">
                {/* Egyszerű vizuális másolat, nem interaktív */}
                <div className="p-3 border rounded-lg bg-white shadow-lg w-80">
                  <div className="flex items-start gap-3">
                    <Checkbox checked={false} disabled />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-sm">
                          {getPermissionDisplayName(activePermission)}
                        </span>
                        {!!activePermission.is_dangerous && (
                          <AlertTriangle className="h-4 w-4 text-amber-500" />
                        )}
                      </div>
                      <p className="text-xs text-gray-600 mb-2">
                        {getPermissionDescription(activePermission)}
                      </p>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          {getCategoryInfo(activePermission.category)?.name || 'Általános'}
                        </Badge>
                        <code className="text-xs text-gray-500 bg-gray-100 px-1 rounded">
                          {activePermission.name}
                        </code>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
          </DragOverlay>
        </DndContext>

        {/* Veszélyes jogosultság figyelmeztetés */}
        {assignedPermissions.some((p) => p.is_dangerous) && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-4 w-4" />
              <span className="font-medium text-sm">Figyelem!</span>
            </div>
            <p className="text-amber-700 text-sm mt-1">
              Ez a szerepkör veszélyes jogosultságokat tartalmaz. Csak megbízható felhasználóknak adja meg!
            </p>
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}