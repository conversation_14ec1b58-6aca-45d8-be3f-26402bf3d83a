<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('postal_code', 10);
            $table->string('street');
            $table->string('city');
            $table->string('address')->nullable(); // Teljes cím összefűzve
            $table->string('phone')->nullable();
            $table->json('emails')->nullable(); // Több email cím tárolása
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            
            $table->index(['name', 'city']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_providers');
    }
};
