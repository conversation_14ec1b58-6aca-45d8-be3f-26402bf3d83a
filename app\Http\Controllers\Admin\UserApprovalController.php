<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;
use App\Notifications\UserApprovalNotification;

class UserApprovalController extends Controller
{
    /**
     * Display pending user approvals
     */
    public function index(): Response
    {
        $user = auth()->user();
        
        // Jogosultság alapú szűrés
        $query = User::with(['contact', 'orgUnit', 'garage'])
            ->where('is_active', false)
            ->whereNotNull('company_type');

        // Ha csak Posta felhasználókat kezelhet
        if ($user->can('manage_posta_users') && !$user->can('manage_users')) {
            $query->where('company_type', 'posta');
        }
        // Ha csak Partner felhasználókat kezelhet
        elseif ($user->can('manage_users') && !$user->can('manage_posta_users')) {
            $query->where('company_type', 'partner');
        }
        // Ha mindkettőt kezelheti, akkor minden inaktív user
        elseif (!$user->can('manage_users') && !$user->can('manage_posta_users')) {
            // Ha nincs jogosultsága, üres lista
            $query->whereRaw('1 = 0');
        }

        $pendingUsers = $query->orderBy('created_at', 'desc')->get();

        return Inertia::render('admin/user-approval/index', [
            'pendingUsers' => $pendingUsers,
            'canManageUsers' => $user->can('manage_users'),
            'canManagePostaUsers' => $user->can('manage_posta_users'),
        ]);
    }

    /**
     * Show user details for approval
     */
    public function show(User $user): JsonResponse
    {
        $currentUser = auth()->user();
        
        // Jogosultság ellenőrzés
        if ($user->company_type === 'posta' && !$currentUser->can('manage_posta_users')) {
            return response()->json(['error' => 'Nincs jogosultsága Posta felhasználók kezeléséhez'], 403);
        }
        
        if ($user->company_type === 'partner' && !$currentUser->can('manage_users')) {
            return response()->json(['error' => 'Nincs jogosultsága Partner felhasználók kezeléséhez'], 403);
        }

        // Csak inaktív felhasználók részletei kérhetők le
        if ($user->is_active) {
            return response()->json(['error' => 'Ez a felhasználó már aktív'], 400);
        }

        $userDetails = $user->load(['contact', 'orgUnit', 'garage']);

        return response()->json([
            'user' => $userDetails,
        ]);
    }

    /**
     * Approve user registration
     */
    public function approve(Request $request, User $user): RedirectResponse
    {
        $currentUser = auth()->user();
        
        // Jogosultság ellenőrzés
        if ($user->company_type === 'posta' && !$currentUser->can('manage_posta_users')) {
            return back()->withErrors(['error' => 'Nincs jogosultsága Posta felhasználók kezeléséhez']);
        }
        
        if ($user->company_type === 'partner' && !$currentUser->can('manage_users')) {
            return back()->withErrors(['error' => 'Nincs jogosultsága Partner felhasználók kezeléséhez']);
        }

        // Csak inaktív felhasználók hagyhatók jóvá
        if ($user->is_active) {
            return back()->withErrors(['error' => 'Ez a felhasználó már aktív']);
        }

        // Felhasználó aktiválása
        $user->update([
            'is_active' => true,
            'approved_at' => now(),
            'approved_by' => $currentUser->id,
        ]);

        // Email értesítés küldése
        $user->notify(new UserApprovalNotification(true, null));

        return back()->with('success', 'Felhasználó sikeresen jóváhagyva és aktiválva.');
    }

    /**
     * Reject user registration
     */
    public function reject(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|min:10|max:1000',
        ], [
            'reason.required' => 'Az elutasítás indoklása kötelező.',
            'reason.min' => 'Az indoklásnak legalább 10 karakter hosszúnak kell lennie.',
            'reason.max' => 'Az indoklás maximum 1000 karakter lehet.',
        ]);

        $currentUser = auth()->user();
        
        // Jogosultság ellenőrzés
        if ($user->company_type === 'posta' && !$currentUser->can('manage_posta_users')) {
            return back()->withErrors(['error' => 'Nincs jogosultsága Posta felhasználók kezeléséhez']);
        }
        
        if ($user->company_type === 'partner' && !$currentUser->can('manage_users')) {
            return back()->withErrors(['error' => 'Nincs jogosultsága Partner felhasználók kezeléséhez']);
        }

        // Csak inaktív felhasználók utasíthatók el
        if ($user->is_active) {
            return back()->withErrors(['error' => 'Ez a felhasználó már aktív']);
        }

        // Email értesítés küldése elutasítás indoklásával
        $user->notify(new UserApprovalNotification(false, $request->reason));

        // Felhasználó törlése (mivel elutasítottuk)
        $user->delete();

        return back()->with('success', 'Felhasználó regisztrációja elutasítva és törölve.');
    }
}
