@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

:root {
    --radius: 0.65rem;
    --background: oklch(0.99 0 0);
    --foreground: oklch(0.2 0.01 150);
    --card: oklch(0.99 0 0);
    --card-foreground: oklch(0.2 0.01 150);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.2 0.01 150);
    --primary: oklch(0.52 0.15 150); /* #426043 */
    --primary-foreground: oklch(0.95 0.02 160);
    --secondary: oklch(0.92 0.01 280);
    --secondary-foreground: oklch(0.2 0.01 280);
    --muted: oklch(0.92 0.01 280);
    --muted-foreground: oklch(0.6 0.02 280);
    --accent: oklch(0.92 0.01 280);
    --accent-foreground: oklch(0.2 0.01 280);
    --destructive: oklch(0.6 0.2 30);
    --border: oklch(0.9 0.01 280);
    --input: oklch(0.9 0.01 280);
    --ring: oklch(0.52 0.15 150);
    --chart-1: oklch(0.65 0.2 60);
    --chart-2: oklch(0.6 0.1 180);
    --chart-3: oklch(0.4 0.08 220);
    --chart-4: oklch(0.82 0.18 80);
    --chart-5: oklch(0.75 0.17 70);
    --sidebar: oklch(0.98 0 0);
    --sidebar-foreground: oklch(0.2 0.01 150);
    --sidebar-primary: oklch(0.52 0.15 150);
    --sidebar-primary-foreground: oklch(0.95 0.02 160);
    --sidebar-accent: oklch(0.92 0.01 280);
    --sidebar-accent-foreground: oklch(0.2 0.01 280);
    --sidebar-border: oklch(0.9 0.01 280);
    --sidebar-ring: oklch(0.52 0.15 150);
}

.dark {
    --background: oklch(0.2 0.01 150);
    --foreground: oklch(0.98 0 0);
    --card: oklch(0.25 0.01 150);
    --card-foreground: oklch(0.98 0 0);
    --popover: oklch(0.25 0.01 150);
    --popover-foreground: oklch(0.98 0 0);
    --primary: oklch(0.48 0.13 150); /* #426043 dark mode */
    --primary-foreground: oklch(0.3 0.06 150);
    --secondary: oklch(0.3 0.01 280);
    --secondary-foreground: oklch(0.98 0 0);
    --muted: oklch(0.3 0.01 280);
    --muted-foreground: oklch(0.7 0.02 280);
    --accent: oklch(0.3 0.01 280);
    --accent-foreground: oklch(0.98 0 0);
    --destructive: oklch(0.7 0.18 20);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.48 0.13 150);
    --chart-1: oklch(0.45 0.2 260);
    --chart-2: oklch(0.6 0.13 160);
    --chart-3: oklch(0.7 0.17 70);
    --chart-4: oklch(0.6 0.25 300);
    --chart-5: oklch(0.62 0.22 10);
    --sidebar: oklch(0.25 0.01 150);
    --sidebar-foreground: oklch(0.98 0 0);
    --sidebar-primary: oklch(0.48 0.13 150);
    --sidebar-primary-foreground: oklch(0.3 0.06 150);
    --sidebar-accent: oklch(0.3 0.01 280);
    --sidebar-accent-foreground: oklch(0.98 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.48 0.13 150);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}
