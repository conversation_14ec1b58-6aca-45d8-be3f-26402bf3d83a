<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ContactLookupController extends Controller
{
    /**
     * Contact lookup by email for registration
     */
    public function lookup(Request $request): JsonResponse
    {
        $email = $request->query('email');
        
        if (!$email) {
            return response()->json([
                'success' => false,
                'message' => 'Email cím szükséges'
            ], 400);
        }

        // Csak @posta.hu domain-t fogadunk el
        if (!str_ends_with(strtolower($email), '@posta.hu')) {
            return response()->json([
                'success' => false,
                'message' => 'Csak @posta.hu domain-ű email címek engedélyezettek'
            ], 400);
        }

        // Contact keresése
        $contact = Contact::with(['orgUnit', 'garage'])
            ->where('email', $email)
            ->first();

        if (!$contact) {
            return response()->json([
                'success' => false,
                'message' => 'A megadott emailcím nem található az adatbázisunkban'
            ]);
        }

        return response()->json([
            'success' => true,
            'contact' => [
                'id' => $contact->id,
                'name' => $contact->name,
                'email' => $contact->email,
                'username' => $contact->username,
                'org_unit_id' => $contact->org_unit_id,
                'garage_id' => $contact->garage_id,
                'org_unit' => $contact->orgUnit ? [
                    'id' => $contact->orgUnit->id,
                    'name' => $contact->orgUnit->name,
                ] : null,
                'garage' => $contact->garage ? [
                    'id' => $contact->garage->id,
                    'name' => $contact->garage->name,
                ] : null,
            ]
        ]);
    }
}
