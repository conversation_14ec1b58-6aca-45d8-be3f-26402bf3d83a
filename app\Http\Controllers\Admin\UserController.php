<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Garage;
use App\Models\OrgUnit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function index()
    {
        return Inertia::render('admin/users/index', [
            'users' => User::with(['roles', 'contact', 'orgUnit', 'garage'])->get(),
            'roles' => Role::all(['id', 'name']),
            'orgUnits' => OrgUnit::orderBy('name')->get(['id', 'name']),
            'garages' => Garage::orderBy('name')->get(['id', 'name']),
            'positions' => User::getExistingPositions(),
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Password::defaults()],
            'is_active' => ['required', 'boolean'],
            'roles' => ['required', 'array'],
            'roles.*' => ['exists:roles,id'],
            'company_type' => ['required', Rule::in(['posta', 'partner'])],

            // Partner fields
            'company_name' => ['required_if:company_type,partner', 'nullable', 'string', 'max:255'],
            'tax_number' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],

            // Posta fields
            'username' => ['required_if:company_type,posta', 'nullable', 'string', 'max:255', 'unique:users,username'],
            'org_unit_id' => ['nullable', 'exists:org_units,id'],
            'garage_id' => ['nullable', 'exists:garages,id'],
        ]);

        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'is_active' => $validated['is_active'],
            'company_type' => $validated['company_type'],
        ];

        if ($validated['company_type'] === 'partner') {
            $userData['company_name'] = $validated['company_name'];
            $userData['tax_number'] = $validated['tax_number'];
            $userData['position'] = $validated['position'];
            $userData['phone'] = $validated['phone'];
        } else { // posta
            $userData['username'] = $validated['username'];
            $userData['org_unit_id'] = $validated['org_unit_id'] ?: null;
            $userData['garage_id'] = $validated['garage_id'] ?: null;
        }

        $user = User::create($userData);
        $user->roles()->sync($validated['roles']);

        return redirect()->route('admin.users.index')->with('success', 'Felhasználó létrehozva.');
    }

    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'password' => ['nullable', 'confirmed', Password::defaults()],
            'is_active' => ['required', 'boolean'],
            'roles' => ['required', 'array'],
            'roles.*' => ['exists:roles,id'],
            'company_type' => ['required', Rule::in(['posta', 'partner'])],

            // Partner fields
            'company_name' => ['required_if:company_type,partner', 'nullable', 'string', 'max:255'],
            'tax_number' => ['nullable', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:255'],

            // Posta fields
            'username' => ['required_if:company_type,posta', 'nullable', 'string', 'max:255', Rule::unique('users')->ignore($user->id)],
            'org_unit_id' => ['nullable', 'exists:org_units,id'],
            'garage_id' => ['nullable', 'exists:garages,id'],
        ]);

        $userData = [
            'name' => $validated['name'],
            'email' => $validated['email'],
            'is_active' => $validated['is_active'],
            'company_type' => $validated['company_type'],
        ];

        if (! empty($validated['password'])) {
            $userData['password'] = Hash::make($validated['password']);
        }

        if ($validated['company_type'] === 'partner') {
            $userData['company_name'] = $validated['company_name'];
            $userData['tax_number'] = $validated['tax_number'];
            $userData['position'] = $validated['position'];
            $userData['phone'] = $validated['phone'];
            // Posta mezők nullázása
            $userData['username'] = null;
            $userData['org_unit_id'] = null;
            $userData['garage_id'] = null;
        } else { // posta
            $userData['username'] = $validated['username'];
            $userData['org_unit_id'] = $validated['org_unit_id'] ?: null;
            $userData['garage_id'] = $validated['garage_id'] ?: null;
            // Partner mezők nullázása
            $userData['company_name'] = null;
            $userData['tax_number'] = null;
            $userData['position'] = null;
            $userData['phone'] = null;
        }

        $user->update($userData);
        $user->roles()->sync($validated['roles']);

        return redirect()->back()->with('success', 'Felhasználó sikeresen módosítva.');
    }

    public function destroy(User $user)
    {
        // Soft delete helyett inaktiválás
        $user->update(['is_active' => false]);

        return redirect()->back()->with('success', 'Felhasználó inaktiválva');
    }

    /**
     * Activate user (for admin approval)
     */
    public function activate(User $user)
    {
        $user->update(['is_active' => true]);

        return redirect()->back()->with('success', 'Felhasználó aktiválva');
    }

    /**
     * Deactivate user
     */
    public function deactivate(User $user)
    {
        $user->update(['is_active' => false]);

        return redirect()->back()->with('success', 'Felhasználó inaktiválva');
    }

    public function create() {}
    
    public function edit(User $user) {}

    public function show(User $user) {}

}
