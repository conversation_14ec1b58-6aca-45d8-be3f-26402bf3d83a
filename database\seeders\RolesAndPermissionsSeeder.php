<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        $permissions = [
            'submit_repair_request',
            'cancel_repair_request',
            'approve_quote_500k',
            'approve_quote_1m',
            'approve_quote_above_1m',
            'view_repair_status',
            'manage_users',
            'manage_roles',
            'view_logs',
            'upload_documents',
            'download_exports',
            'access_vir',
            'access_system_admin_features',
        ];

        foreach ($permissions as $perm) {
            Permission::firstOrCreate(['name' => $perm]);
        }

        $roles = [
            [
                'name' => 'SystemAdmin',
                'limit' => null,
                'permissions' => $permissions,
            ],
            [
                'name' => 'PostaAdmin',
                'limit' => null,
                'permissions' => [
                    'submit_repair_request',
                    'cancel_repair_request',
                    'view_repair_status',
                    'upload_documents',
                    'manage_users',
                    'manage_roles',
                    'view_logs',
                    'download_exports',
                    'access_vir',
                ],
            ],
            [
                'name' => 'KülsőAdmin',
                'limit' => null,
                'permissions' => [
                    'submit_repair_request',
                    'cancel_repair_request',
                    'view_repair_status',
                    'upload_documents',
                    'manage_users',
                    'manage_roles',
                    'view_logs',
                ],
            ],
            [
                'name' => 'Diszpécser',
                'limit' => null,
                'permissions' => [
                    'submit_repair_request',
                    'cancel_repair_request',
                    'view_repair_status',
                    'upload_documents',
                ],
            ],
            [
                'name' => 'Műszaki ellenőr',
                'limit' => 500000,
                'permissions' => [
                    'submit_repair_request',
                    'cancel_repair_request',
                    'view_repair_status',
                    'approve_quote_500k',
                    'upload_documents',
                ],
            ],
            [
                'name' => 'Műszaki menedzser',
                'limit' => 1000000,
                'permissions' => [
                    'submit_repair_request',
                    'cancel_repair_request',
                    'view_repair_status',
                    'approve_quote_500k',
                    'approve_quote_1m',
                    'upload_documents',
                ],
            ],
            [
                'name' => 'Osztályvezető',
                'limit' => null,
                'permissions' => [
                    'approve_quote_above_1m',
                    'access_vir',
                    'download_exports',
                ],
            ],
            [
                'name' => 'MP+PF menedzsment',
                'limit' => null,
                'permissions' => [
                    'access_vir',
                    'download_exports',
                ],
            ],
            [
                'name' => 'Betekintő',
                'limit' => null,
                'permissions' => [
                    'view_repair_status',
                ],
            ],
        ];

        foreach ($roles as $roleData) {
            $role = Role::firstOrCreate(['name' => $roleData['name']]);
            $role->update(['approval_limit' => $roleData['limit']]);
            $role->syncPermissions($roleData['permissions']);
        }
    }
}
