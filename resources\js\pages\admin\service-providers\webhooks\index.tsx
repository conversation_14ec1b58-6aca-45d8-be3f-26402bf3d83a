import { Head, Link, router } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Activity, ArrowLeft, Building2, Pause, Plus, TestTube, Webhook } from 'lucide-react';

import { useState } from 'react';

import WebhookForm from '@/components/admin/WebhookForm';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AppLayout from '@/layouts/app-layout';

interface ServiceProviderWebhook {
    id: number;
    name: string;
    event_type: string;
    url: string;
    method: string;
    is_active: boolean;
    timeout: number;
    retry_attempts: number;
    last_called_at?: string;
    last_status?: string;
    created_at: string;
}

interface ServiceProvider {
    id: number;
    name: string;
    city: string;
}

interface Props {
    serviceProvider: ServiceProvider;
    webhooks: ServiceProviderWebhook[];
    httpMethods: string[];
}

export default function ServiceProviderWebhooksIndex({ serviceProvider, webhooks, httpMethods }: Props) {
    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<ServiceProviderWebhook | null>(null);

    const handleToggle = (webhook: ServiceProviderWebhook) => {
        router.patch(route('admin.service-providers.webhooks.toggle', [serviceProvider.id, webhook.id]));
    };

    const handleTest = async (webhook: ServiceProviderWebhook) => {
        try {
            const response = await fetch(route('admin.service-providers.webhooks.test', [serviceProvider.id, webhook.id]), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            const result = await response.json();

            if (result.success) {
                alert('Webhook teszt sikeres!');
            } else {
                alert(`Webhook teszt sikertelen: ${result.message}`);
            }
        } catch (error) {
            alert('Hiba történt a webhook tesztelése során.');
        }
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const handleEdit = (webhook: ServiceProviderWebhook) => {
        setEditing(webhook);
        setOpen(true);
    };

    const confirmDelete = (webhook: ServiceProviderWebhook) => {
        if (confirm(`Biztosan törli a "${webhook.name}" webhookot?`)) {
            router.delete(route('admin.service-providers.webhooks.destroy', [serviceProvider.id, webhook.id]));
        }
    };

    const getStatusBadge = (status?: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                        Sikeres
                    </Badge>
                );
            case 'failed':
                return <Badge variant="destructive">Sikertelen</Badge>;
            case 'error':
                return <Badge variant="destructive">Hiba</Badge>;
            default:
                return <Badge variant="secondary">Nincs adat</Badge>;
        }
    };

    const columns: ColumnDef<ServiceProviderWebhook>[] = [
        {
            header: 'Webhook neve',
            accessorKey: 'name',
            cell: ({ row }) => (
                <div>
                    <div className="font-medium">{row.original.name}</div>
                </div>
            ),
        },
        {
            header: 'URL és metódus',
            cell: ({ row }) => (
                <div>
                    <div className="font-mono text-sm">{row.original.method}</div>
                    <div className="max-w-xs truncate text-xs text-gray-500" title={row.original.url}>
                        {row.original.url}
                    </div>
                </div>
            ),
        },
        {
            header: 'Beállítások',
            cell: ({ row }) => (
                <div className="text-sm">
                    <div>Timeout: {row.original.timeout}s</div>
                    <div>Újrapróbálkozás: {row.original.retry_attempts}x</div>
                </div>
            ),
        },
        {
            header: 'Utolsó hívás',
            cell: ({ row }) => (
                <div className="space-y-1">
                    {getStatusBadge(row.original.last_status)}
                    {row.original.last_called_at && (
                        <div className="text-xs text-gray-500">{new Date(row.original.last_called_at).toLocaleString('hu-HU')}</div>
                    )}
                </div>
            ),
        },
        {
            header: 'Állapot',
            accessorKey: 'is_active',
            cell: ({ row }) => (
                <Badge variant={row.original.is_active ? 'default' : 'secondary'}>{row.original.is_active ? 'Aktív' : 'Inaktív'}</Badge>
            ),
        },
    ];

    return (
        <AppLayout>
            <Head title={`${serviceProvider.name} - Webhookok`} />

            <Dialog open={open} onOpenChange={setOpen}>
                <div className="space-y-6">
                    {/* Header */}
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                            <Button variant="outline" size="sm" asChild>
                                <Link href={route('admin.service-providers.index', serviceProvider.id)}>
                                    <ArrowLeft className="mr-2 h-4 w-4" />
                                    Vissza
                                </Link>
                            </Button>
                            <div>
                                <h1 className="flex items-center gap-2 text-2xl font-bold text-gray-900">
                                    <Webhook className="h-6 w-6 text-blue-600" />
                                    Webhookok kezelése
                                </h1>
                                <p className="flex items-center gap-2 text-gray-600">
                                    <Building2 className="h-4 w-4" />
                                    {serviceProvider.name} - {serviceProvider.city}
                                </p>
                            </div>
                        </div>
                        <DialogTrigger asChild>
                            <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                <Plus className="mr-2 h-4 w-4" />
                                Új webhook
                            </Button>
                        </DialogTrigger>
                    </div>

                    {/* Webhookok statisztika */}
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                        <div className="rounded-lg border bg-white p-4">
                            <div className="flex items-center gap-2">
                                <Webhook className="h-5 w-5 text-blue-600" />
                                <div>
                                    <p className="text-sm text-gray-500">Összes webhook</p>
                                    <p className="text-2xl font-bold">{webhooks.length}</p>
                                </div>
                            </div>
                        </div>
                        <div className="rounded-lg border bg-white p-4">
                            <div className="flex items-center gap-2">
                                <Activity className="h-5 w-5 text-green-600" />
                                <div>
                                    <p className="text-sm text-gray-500">Aktív</p>
                                    <p className="text-2xl font-bold">{webhooks.filter((w) => w.is_active).length}</p>
                                </div>
                            </div>
                        </div>
                        <div className="rounded-lg border bg-white p-4">
                            <div className="flex items-center gap-2">
                                <Pause className="h-5 w-5 text-gray-600" />
                                <div>
                                    <p className="text-sm text-gray-500">Inaktív</p>
                                    <p className="text-2xl font-bold">{webhooks.filter((w) => !w.is_active).length}</p>
                                </div>
                            </div>
                        </div>
                        <div className="rounded-lg border bg-white p-4">
                            <div className="flex items-center gap-2">
                                <TestTube className="h-5 w-5 text-amber-600" />
                                <div>
                                    <p className="text-sm text-gray-500">Sikeres hívások</p>
                                    <p className="text-2xl font-bold">{webhooks.filter((w) => w.last_status === 'success').length}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Webhookok táblázat */}
                    <div className="rounded-lg border bg-white">
                        {webhooks.length > 0 ? (
                            <div>
                                <div className="border-b p-4">
                                    <div className="flex items-center justify-between">
                                        <h3 className="text-lg font-medium">Regisztrált webhookok ({webhooks.length} db)</h3>
                                    </div>
                                </div>
                                <DataTable
                                    columns={columns}
                                    data={webhooks}
                                    onEdit={handleEdit}
                                    onDelete={confirmDelete}
                                    pageSize={10}
                                    showExportButton={false}
                                />
                            </div>
                        ) : (
                            <div className="py-12 text-center">
                                <Webhook className="mx-auto mb-4 h-16 w-16 text-gray-300" />
                                <h3 className="mb-2 text-lg font-medium text-gray-900">Még nincsenek webhookok</h3>
                                <p className="mb-6 text-gray-500">Hozza létre az első webhookot a szervizzel való kommunikáció megkezdéséhez.</p>
                                <DialogTrigger asChild>
                                    <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                        <Plus className="mr-2 h-4 w-4" />
                                        Első webhook létrehozása
                                    </Button>
                                </DialogTrigger>
                            </div>
                        )}
                    </div>
                </div>
                <DialogContent
                    className="flex max-h-[90vh] max-w-5xl flex-col p-0 md:max-w-6xl"
                    onInteractOutside={(e) => {
                        e.preventDefault();
                    }}
                >
                    <DialogHeader className="p-6 pb-0">
                        <DialogTitle>{editing ? 'Webhook szerkesztése' : 'Új webhook létrehozása'}</DialogTitle>
                        <DialogDescription>
                            {editing ? 'Módosítsa a webhook adatait.' : 'Töltse ki az űrlapot az új webhook beállításához.'}
                        </DialogDescription>
                    </DialogHeader>
                    <WebhookForm serviceProvider={serviceProvider} webhook={editing} httpMethods={httpMethods} onCancel={() => setOpen(false)} />
                </DialogContent>
            </Dialog>
        </AppLayout>
    );
}
