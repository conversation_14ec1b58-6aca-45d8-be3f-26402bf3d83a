import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SingleSelect } from '@/components/ui/single-select';
import { useForm } from '@inertiajs/react';
import { Building2, Car, Mail, User, UserCheck } from 'lucide-react';
import { useEffect, useState } from 'react';

type OrgUnit = {
    id: number;
    name: string;
};

type Garage = {
    id: number;
    name: string;
    postal_code?: string;
    city?: string;
    address?: string;
};

type Contact = {
    id?: number;
    name: string;
    email: string;
    username: string;
    org_unit_id?: number;
    garage_id?: number;
    org_unit?: OrgUnit;
    garage?: Garage;
};

type Props = {
    contact: Contact | null;
    orgUnits: OrgUnit[];
    garages: Garage[];
    onClose: () => void;
    onCreateOrgUnit?: (name: string) => Promise<OrgUnit>;
    onCreateGarage?: (name: string) => Promise<Garage>;
};

export function ContactForm({ contact, orgUnits, garages, onClose, onCreateOrgUnit, onCreateGarage }: Props) {
    const [orgUnitList, setOrgUnitList] = useState<OrgUnit[]>(orgUnits);
    const [garageList, setGarageList] = useState<Garage[]>(garages);
    const { data, setData, post, put, processing, errors } = useForm({
        name: contact?.name || '',
        email: contact?.email || '',
        username: contact?.username || '',
        org_unit_id: contact?.org_unit_id?.toString() || '',
        garage_id: contact?.garage_id?.toString() || '',
    });

    useEffect(() => {
        if (contact) {
            setData({
                name: contact.name,
                email: contact.email,
                username: contact.username,
                org_unit_id: contact.org_unit_id?.toString() || '',
                garage_id: contact.garage_id?.toString() || '',
            });
        }
    }, [contact]);

    const submit = (e: React.FormEvent) => {
        e.preventDefault();

        if (contact) {
            put(`/admin/contacts/${contact.id}`, {
                onSuccess: () => {
                    onClose();
                },
            });
        } else {
            post('/admin/contacts', {
                onSuccess: () => {
                    onClose();
                },
            });
        }
    };

    return (
        <div className="flex h-full flex-col">
            <div className="flex-1 overflow-y-auto px-6">
                <form id="contact-form" onSubmit={submit} className="space-y-6 py-4">
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Név</Label>
                            <div className="relative">
                                <User className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="name"
                                    className="pl-10"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    placeholder="Teljes név"
                                    required
                                />
                            </div>
                            {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="email">Email cím</Label>
                            <div className="relative">
                                <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="email"
                                    type="email"
                                    className="pl-10"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    required
                                />
                            </div>
                            {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="username">Felhasználónév</Label>
                            <div className="relative">
                                <UserCheck className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="username"
                                    className="pl-10"
                                    value={data.username}
                                    onChange={(e) => setData('username', e.target.value)}
                                    placeholder="felhasznalonev"
                                    required
                                />
                            </div>
                            {errors.username && <p className="text-sm text-red-500">{errors.username}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="org_unit_id">Szervezeti egység</Label>
                            <SingleSelect
                                value={data.org_unit_id || ''}
                                onChange={(value) => setData('org_unit_id', value ? String(value) : '')}
                                options={[
                                    { value: '', label: 'Nincs kiválasztva' },
                                    ...orgUnitList.map((unit) => ({
                                        value: unit.id.toString(),
                                        label: unit.name,
                                    })),
                                ]}
                                placeholder="Válassz szervezeti egységet..."
                                icon={<Building2 className="h-4 w-4 text-muted-foreground" />}
                                onCreate={
                                    onCreateOrgUnit
                                        ? async (name: string) => {
                                              const newUnit = await onCreateOrgUnit(name);
                                              setOrgUnitList((prev) => [...prev, newUnit]);
                                              setData('org_unit_id', newUnit.id.toString());
                                              return {
                                                  value: newUnit.id.toString(),
                                                  label: newUnit.name,
                                              };
                                          }
                                        : undefined
                                }
                                createPrefix="Új szervezeti egység: "
                            />
                            {errors.org_unit_id && <p className="text-sm text-red-500">{errors.org_unit_id}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="garage_id">Garázs</Label>
                            <SingleSelect
                                value={data.garage_id || ''}
                                onChange={(value) => setData('garage_id', value ? String(value) : '')}
                                options={[
                                    { value: '', label: 'Nincs kiválasztva' },
                                    ...garageList.map((garage) => ({
                                        value: garage.id.toString(),
                                        label: garage.name,
                                        //label: `${garage.name} (${garage.postal_code ?? ''} ${garage.city ?? ''})`,
                                    })),
                                ]}
                                placeholder="Válassz garázst..."
                                icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                onCreate={
                                    onCreateGarage
                                        ? async (name: string) => {
                                              const newGarage = await onCreateGarage(name);
                                              setGarageList((prev) => [...prev, newGarage]);
                                              setData('garage_id', newGarage.id.toString());
                                              return {
                                                  value: newGarage.id.toString(),
                                                  label: `${newGarage.name} (${newGarage.postal_code ?? ''} ${newGarage.city ?? ''})`,
                                              };
                                          }
                                        : undefined
                                }
                                createPrefix="Új garázs: "
                            />
                            {errors.garage_id && <p className="text-sm text-red-500">{errors.garage_id}</p>}
                        </div>
                        {/*                 Példa a allowCustomValue használatára
                         */}
                        {/*                 <div className="space-y-2">
                    <Label htmlFor="test-org-unit">Teszt Szervezeti egység (allowCustomValue)</Label>
                    <SingleSelect
                        value={value}
                        onChange={(newValue) => {
                            // ha nem szerepel még a listában, akkor hozzáadjuk
                            const exists = orgUnitList.some((unit) => unit.name.toLowerCase() === String(newValue).toLowerCase());
                            if (!exists && typeof newValue === 'string') {
                                const newId = Date.now(); // vagy egy random number/id
                                const newUnit = { id: newId, name: newValue };
                                setOrgUnitList((prev) => [...prev, newUnit]);
                                setValue(newId.toString());
                            } else {
                                setValue(newValue);
                            }
                        }}
                        placeholder="Válassz vagy írj be bármit..."
                        options={[
                            { value: '', label: 'Nincs kiválasztva' },
                            ...orgUnitList.map((unit) => ({
                                value: unit.id.toString(),
                                label: unit.name,
                            })),
                        ]}
                        allowCustomValue={true}
                    />
                </div>
 */}{' '}
                    </div>
                </form>
            </div>

            <DialogFooter className="px-0">
                <Button type="button" variant="outline" onClick={onClose}>
                    Mégse
                </Button>
                <Button type="submit" form="contact-form" disabled={processing}>
                    {processing ? 'Mentés...' : contact ? 'Frissítés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </div>
    );
}
