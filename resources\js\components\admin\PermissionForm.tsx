// components/admin/PermissionForm.tsx

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SingleSelect } from '@/components/ui/single-select';
import { Textarea } from '@/components/ui/textarea';
import { PERMISSION_CATEGORIES } from '@/utils/permissions';
import { useForm } from '@inertiajs/react';
import { FormEventHandler, useEffect } from 'react';

interface PermissionFormProps {
    permission: {
        id?: number;
        name: string;
        display_name?: string;
        description?: string;
        category: string;
        is_dangerous: boolean;
        sort_order: number;
    } | null;
    onClose: () => void;
}

export default function PermissionForm({ permission, onClose }: PermissionFormProps) {
    const { data, setData, post, put, processing, errors, reset, wasSuccessful } = useForm({
        name: permission?.name || '',
        display_name: permission?.display_name || '',
        description: permission?.description || '',
        category: permission?.category || 'general',
        is_dangerous: permission?.is_dangerous || false,
        sort_order: permission?.sort_order || 0,
    });

    useEffect(() => {
        if (wasSuccessful) onClose();
    }, [wasSuccessful]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();
        if (permission?.id) {
            put(`/admin/permissions/${permission.id}`);
        } else {
            post('/admin/permissions');
        }
    };

    // Kategória opciók előkészítése
    const categoryOptions = PERMISSION_CATEGORIES.map((cat) => ({ value: cat.id, label: cat.name }));

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <h2 className="text-lg font-semibold">{permission ? 'Jogosultság szerkesztése' : 'Új jogosultság'}</h2>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Technikai név */}
                <div className="space-y-2">
                    <Label htmlFor="name">Technikai név</Label>
                    <Input
                        id="name"
                        type="text"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        disabled={processing}
                        placeholder="pl. manage_users"
                        className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && <div className="text-sm text-red-500">{errors.name}</div>}
                </div>

                {/* Magyar megnevezés */}
                <div className="space-y-2">
                    <Label htmlFor="display_name">Magyar megnevezés</Label>
                    <Input
                        id="display_name"
                        type="text"
                        value={data.display_name}
                        onChange={(e) => setData('display_name', e.target.value)}
                        disabled={processing}
                        placeholder="pl. Felhasználók kezelése"
                    />
                    {errors.display_name && <div className="text-sm text-red-500">{errors.display_name}</div>}
                </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                {/* Kategória */}
                <div className="space-y-2">
                    <Label>Kategória</Label>
                    <SingleSelect
                        options={categoryOptions}
                        value={data.category}
                        onChange={(value) => setData('category', String(value ?? ''))}
                        placeholder="Válassz kategóriát..."
                        allowCustomValue={false}
                        disabled={processing}
                    />
                    {errors.category && <div className="text-sm text-red-500">{errors.category}</div>}
                </div>

                {/* Rendezési sorrend */}
                <div className="space-y-2">
                    <Label htmlFor="sort_order">Rendezési sorrend</Label>
                    <Input
                        id="sort_order"
                        type="number"
                        value={data.sort_order}
                        onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
                        disabled={processing}
                        min="0"
                        placeholder="0"
                    />
                    {errors.sort_order && <div className="text-sm text-red-500">{errors.sort_order}</div>}
                </div>
            </div>

            {/* Leírás */}
            <div className="space-y-2">
                <Label htmlFor="description">Leírás</Label>
                <Textarea
                    id="description"
                    value={data.description || ''}
                    onChange={(e) => setData('description', e.target.value)}
                    disabled={processing}
                    placeholder="Részletes leírás a jogosultság funkciójáról..."
                    rows={3}
                />
                {errors.description && <div className="text-sm text-red-500">{errors.description}</div>}
            </div>

            {/* Veszélyes jogosultság */}
            <div className="flex items-center space-x-2">
                <input
                    type="checkbox"
                    id="is_dangerous"
                    checked={data.is_dangerous}
                    onChange={(e) => setData('is_dangerous', e.target.checked)}
                    disabled={processing}
                    className="rounded border-gray-300"
                />
                <Label htmlFor="is_dangerous" className="cursor-pointer">
                    Veszélyes jogosultság
                </Label>
            </div>
            {!!data.is_dangerous && (
                <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
                    <p className="text-sm text-amber-700">
                        ⚠️ Ez a jogosultság veszélyesként lesz megjelölve. Csak megbízható felhasználóknak adja meg!
                    </p>
                </div>
            )}

            <div className="flex justify-end gap-2 border-t pt-4">
                <Button type="button" variant="outline" onClick={onClose}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {processing ? 'Mentés...' : permission?.id ? 'Mentés' : 'Létrehozás'}
                </Button>
            </div>
        </form>
    );
}
