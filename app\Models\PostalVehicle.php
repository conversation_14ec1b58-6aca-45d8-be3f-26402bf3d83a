<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PostalVehicle extends Model
{
    use HasFactory;

    protected $fillable = [
        'license_plate',
        'type', // Keep for backward compatibility during migration
        'manufacturer',
        'model',
        'chassis_number',
        'category',
        'contract_classification',
        'manufacturing_year',
        'fuel_type',
        'authority_inspection_valid_until',
        'garage_id',
        'garage_central_email',
        'service_provider_id',
        'technical_staff_name',
        'technical_staff_email',
        'technical_manager_name',
        'technical_manager_email',
    ];

    protected $casts = [
        'authority_inspection_valid_until' => 'date',
        'manufacturing_year' => 'integer',
    ];

    /**
     * Kapcsolat a garázzsal
     */
    public function garage(): BelongsTo
    {
        return $this->belongsTo(Garage::class);
    }

    /**
     * Kapcsolat a szervizzel
     */
    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    /**
     * Keresés scope
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('license_plate', 'like', "%{$search}%")
              ->orWhere('type', 'like', "%{$search}%") // Keep for backward compatibility
              ->orWhere('manufacturer', 'like', "%{$search}%")
              ->orWhere('model', 'like', "%{$search}%")
              ->orWhere('chassis_number', 'like', "%{$search}%")
              ->orWhere('technical_staff_name', 'like', "%{$search}%")
              ->orWhere('technical_manager_name', 'like', "%{$search}%");
        });
    }

    /**
     * Kategória scope
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Garázs scope
     */
    public function scopeByGarage($query, $garageId)
    {
        return $query->where('garage_id', $garageId);
    }

    /**
     * Szerviz scope
     */
    public function scopeByServiceProvider($query, $serviceProviderId)
    {
        return $query->where('service_provider_id', $serviceProviderId);
    }

    /**
     * Lejáró hatósági vizsga scope (30 napon belül)
     */
    public function scopeExpiringInspection($query)
    {
        return $query->where('authority_inspection_valid_until', '<=', now()->addDays(30));
    }

    /**
     * Lejárt hatósági vizsga scope
     */
    public function scopeExpiredInspection($query)
    {
        return $query->where('authority_inspection_valid_until', '<', now());
    }
}
