import { Head, useForm, usePage } from '@inertiajs/react';
import { <PERSON>hak<PERSON>, <PERSON>ader<PERSON>ircle, MailCheck } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>and<PERSON>, useEffect, useState } from 'react';

import PasswordInput from '@/components/form/password-input';
import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SingleSelect } from '@/components/ui/single-select';
import useTranslations from '@/hooks/use-translations';
import AuthLayout from '@/layouts/auth-layout';
import { cn } from '@/lib/utils';
import { Briefcase, Building2, Car, Hash, Mail, Phone, User } from 'lucide-react';

type Contact = {
    id: number;
    name: string;
    email: string;
    username: string;
    org_unit_id?: number;
    garage_id?: number;
    org_unit?: { id: number; name: string };
    garage?: { id: number; name: string };
};

type OrgUnit = { id: number; name: string };
type Garage = { id: number; name: string };

type RegisterForm = {
    name: string;
    username: string;
    email: string;
    password: string;
    password_confirmation: string;
    company_type: 'posta' | 'partner' | '';
    company_name: string;
    tax_number: string;
    position: string;
    phone: string;
    org_unit_id: string;
    garage_id: string;
};

type PageProps = {
    orgUnits: OrgUnit[];
    garages: Garage[];
    positions: { id: string; name: string }[];
};

export default function Register() {
    const { orgUnits, garages, positions } = usePage<PageProps>().props;

    const [positionList, setPositionList] = useState<{ value: string; label: string }[]>(
        positions.map((pos) => ({ value: pos.id, label: pos.name })),
    );

    const handlePositionChange = (value: string | number | null) => {
        const val = value ? String(value) : '';
        setData('position', val);
        if (val && !positionList.find((opt) => opt.value === val)) {
            setPositionList((prev) => [...prev, { value: val, label: val }]);
        }
    };

    const { data, setData, post, processing, errors, reset } = useForm<Required<RegisterForm>>({
        name: '',
        username: '',
        email: '',
        password: '',
        password_confirmation: '',
        company_type: 'posta',
        company_name: '',
        tax_number: '',
        position: '',
        phone: '',
        org_unit_id: '',
        garage_id: '',
    });

    const [contactData, setContactData] = useState<Contact | null>(null);
    const [isPostaDomain, setIsPostaDomain] = useState(false);
    const [isContactFound, setIsContactFound] = useState(false);

    // Email validáció és contact lookup
    useEffect(() => {
        const email = data.email.toLowerCase();
        const isPostaEmail = email.endsWith('@posta.hu');
        setIsPostaDomain(isPostaEmail);

        // Automatikus váltás Posta típusra, ha @posta.hu emailt írnak be
        if (data.company_type === 'partner' && isPostaEmail) {
            setData('company_type', 'posta');
            return; // Kilépünk, hogy a következő render ciklusban a 'posta' logikája fusson le
        }

        if (data.company_type === 'posta' && email.length > 3) {
            // Contact lookup API hívás
            fetch(route('api.contacts.lookup', { email: email }))
                .then((response) => response.json())
                .then((result) => {
                    if (result.success && result.contact) {
                        setContactData(result.contact);
                        setIsContactFound(true);
                        // Automatikus kitöltés
                        setData((prev) => ({
                            ...prev,
                            name: result.contact.name,
                            username: result.contact.username,
                            org_unit_id: result.contact.org_unit_id?.toString() || '',
                            garage_id: result.contact.garage_id?.toString() || '',
                        }));
                    } else {
                        setContactData(null);
                        setIsContactFound(false);
                    }
                })
                .catch(() => {
                    setContactData(null);
                    setIsContactFound(false);
                });
        } else {
            setContactData(null);
            setIsContactFound(false);
            // Ha a Posta típus van kiválasztva, de az email mező üres vagy túl rövid,
            // töröljük a korábban automatikusan kitöltött adatokat.
            if (data.company_type === 'posta') {
                setData((prev) => ({
                    ...prev,
                    name: '',
                    username: '',
                    org_unit_id: '',
                    garage_id: '',
                }));
            }
        }
    }, [data.email, data.company_type]);

    // Company type változás kezelése
    useEffect(() => {
        if (data.company_type === 'posta') {
            setData((prev) => ({
                ...prev,
                company_name: '',
                tax_number: '',
                position: '',
                phone: '',
            }));
        } else if (data.company_type === 'partner') {
            setData((prev) => ({
                ...prev,
                username: '',
                org_unit_id: '',
                garage_id: '',
            }));
        }
    }, [data.company_type]);

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('register'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    const { t } = useTranslations('auth');

    return (
        <AuthLayout title="Regisztráció" description="Hozzon létre egy új fiókot" two_column={true}>
            <Head title="Regisztráció" />
            <form onSubmit={submit}>
                <div className="grid grid-cols-1 gap-x-6 gap-y-6 md:grid-cols-2">
                    {/* Cégválasztó */}
                    <div className="grid gap-2 md:col-span-2">
                        <RadioGroup
                            value={data.company_type}
                            onValueChange={(value) => setData('company_type', value as 'posta' | 'partner')}
                            className="flex items-center gap-6"
                        >
                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="posta" id="posta" />
                                <label htmlFor="posta" className="flex cursor-pointer items-center gap-2">
                                    <MailCheck className="h-4 w-4 text-muted-foreground" />
                                    <span>Magyar Posta Zrt.</span>
                                </label>
                            </div>

                            <div className="flex items-center space-x-2">
                                <RadioGroupItem value="partner" id="partner" />
                                <label htmlFor="partner" className="flex cursor-pointer items-center gap-2">
                                    <Handshake className="h-4 w-4 text-muted-foreground" />
                                    <span>Más partner</span>
                                </label>
                            </div>
                        </RadioGroup>
                        <InputError message={errors.company_type} className="mt-2" />
                    </div>

                    {/* Email cím */}
                    <div className="grid gap-2 md:col-span-2">
                        <Label htmlFor="email">Email cím</Label>
                        <div className="relative">
                            <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input
                                id="email"
                                type="email"
                                required
                                tabIndex={2}
                                autoComplete="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                disabled={processing}
                                placeholder={data.company_type === 'posta' ? '<EMAIL>' : '<EMAIL>'}
                                className="pl-10"
                            />
                        </div>
                        {data.company_type === 'posta' && data.email.length > 9 && !isContactFound && (
                            <p className="text-sm text-red-500">
                                A megadott emailcím nem található az adatbázisunkban, az MP Zrt. által megadott adatok között. Kérjük adjon meg egy
                                megfelelő email címet!
                            </p>
                        )}
                        <InputError message={errors.email} />
                    </div>

                    {/* Név */}
                    <div className={cn('grid gap-2', data.company_type === 'partner' && 'md:col-span-2')}>
                        <Label htmlFor="name">Név</Label>
                        <div className="relative">
                            <User className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input
                                id="name"
                                type="text"
                                required
                                tabIndex={1}
                                autoComplete="name"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                disabled={processing}
                                placeholder="Teljes név"
                                className="pl-10"
                            />
                        </div>
                        <InputError message={errors.name} className="mt-2" />
                    </div>

                    {/* Felhasználónév */}
                    {data.company_type === 'posta' && (
                        <div className="grid gap-2">
                            <Label htmlFor="username">Felhasználónév</Label>
                            <div className="relative">
                                <User className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                                <Input
                                    id="username"
                                    type="text"
                                    required
                                    autoComplete="username"
                                    value={data.username}
                                    onChange={(e) => setData('username', e.target.value)}
                                    disabled={processing || (isPostaDomain && isContactFound)}
                                    placeholder="Felhasználónév"
                                    className="pl-10"
                                />
                            </div>
                            <InputError message={errors.username} className="mt-2" />
                        </div>
                    )}

                    {/* Posta specifikus mezők */}
                    {data.company_type === 'posta' && (
                        <div className="contents">
                            <div className="grid gap-2">
                                <Label htmlFor="org_unit_id">Szervezeti egység</Label>
                                <SingleSelect
                                    value={data.org_unit_id}
                                    onChange={(value) => setData('org_unit_id', value ? String(value) : '')}
                                    options={[
                                        { value: '', label: 'Válasszon szervezeti egységet...' },
                                        ...orgUnits.map((unit) => ({
                                            value: unit.id.toString(),
                                            label: unit.name,
                                        })),
                                    ]}
                                    placeholder="Szervezeti egység"
                                    icon={<Building2 className="h-4 w-4 text-muted-foreground" />}
                                    disabled={processing}
                                />
                                <InputError message={errors.org_unit_id} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="garage_id">Posta Garázs</Label>
                                <SingleSelect
                                    value={data.garage_id}
                                    onChange={(value) => setData('garage_id', value ? String(value) : '')}
                                    options={[
                                        { value: '', label: 'Válasszon garázst...' },
                                        ...garages.map((garage) => ({
                                            value: garage.id.toString(),
                                            label: garage.name,
                                        })),
                                    ]}
                                    placeholder="Garázs"
                                    icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                    disabled={processing}
                                />
                                <InputError message={errors.garage_id} />
                            </div>
                        </div>
                    )}

                    {/* Partner specifikus mezők */}
                    {data.company_type === 'partner' && (
                        <div className="contents">
                            <div className="grid gap-2">
                                <Label htmlFor="company_name">Cégnév</Label>
                                <div className="relative">
                                    <Building2 className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="company_name"
                                        type="text"
                                        required
                                        value={data.company_name}
                                        onChange={(e) => setData('company_name', e.target.value)}
                                        disabled={processing}
                                        placeholder="Cégnév"
                                        className="pl-10"
                                    />
                                </div>
                                <InputError message={errors.company_name} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="tax_number">Adószám</Label>
                                <div className="relative">
                                    <Hash className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="tax_number"
                                        type="text"
                                        required
                                        value={data.tax_number}
                                        onChange={(e) => setData('tax_number', e.target.value)}
                                        disabled={processing}
                                        placeholder="12345678-1-23"
                                        className="pl-10"
                                    />
                                </div>
                                <InputError message={errors.tax_number} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="position">Munkakör</Label>
                                <SingleSelect
                                    value={data.position}
                                    onChange={handlePositionChange}
                                    options={positionList}
                                    placeholder="Munkakör"
                                    icon={<Briefcase className="h-4 w-4 text-muted-foreground" />}
                                    allowCustomValue={true}
                                    createPrefix="Új munkakör: "
                                    disabled={processing}
                                />
                                <InputError message={errors.position} />
                            </div>

                            <div className="grid gap-2">
                                <Label htmlFor="phone">Telefonszám</Label>
                                <div className="relative">
                                    <Phone className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="phone"
                                        type="tel"
                                        required
                                        value={data.phone}
                                        onChange={(e) => setData('phone', e.target.value)}
                                        disabled={processing}
                                        placeholder="+36 30 123 4567"
                                        className="pl-10"
                                    />
                                </div>
                                <InputError message={errors.phone} />
                            </div>
                        </div>
                    )}

                    {/* Jelszó mezők */}
                    <PasswordInput
                        id="password"
                        name="password"
                        value={data.password}
                        tabIndex={3}
                        onChange={(e) => setData('password', e.target.value)}
                        autoComplete="new-password"
                        disabled={processing}
                        error={errors.password}
                        placeholder="Jelszó"
                        label="Jelszó"
                    />

                    <PasswordInput
                        id="password_confirmation"
                        name="password_confirmation"
                        value={data.password_confirmation}
                        tabIndex={4}
                        onChange={(e) => setData('password_confirmation', e.target.value)}
                        autoComplete="new-password"
                        disabled={processing}
                        error={errors.password_confirmation}
                        placeholder="Jelszó megerősítése"
                        label="Jelszó megerősítése"
                    />

                    {/* Submit gomb */}
                    <div className="flex flex-col gap-4 md:col-span-2">
                        <Button
                            type="submit"
                            className="mt-2 w-full"
                            tabIndex={5}
                            disabled={processing || (data.company_type === 'posta' && (!isContactFound || !isPostaDomain))}
                        >
                            {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                            Fiók létrehozása
                        </Button>

                        {/* Inaktív fiók figyelmeztetés */}
                        <div className="rounded-md bg-amber-50 p-3 text-center text-sm text-amber-600">
                            <strong>Figyelem:</strong> A regisztráció után a fiókja inaktív lesz. Az adminisztrátor jóváhagyása szükséges a
                            bejelentkezéshez.
                        </div>
                    </div>
                </div>

                <div className="mt-6 text-center text-sm text-muted-foreground">
                    Már van fiókja?{' '}
                    <TextLink href={route('login')} tabIndex={6}>
                        Bejelentkezés
                    </TextLink>
                </div>
            </form>
        </AuthLayout>
    );
}
