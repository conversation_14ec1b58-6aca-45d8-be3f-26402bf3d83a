# Kapcsolattartók Admin Modul

## Áttekintés

A Kapcsolattartók modul a Postai dolgozók nyilvántartását végzi. A modul teljes CRUD funkcionalitást biztosít, Excel import/export lehetőségekkel és fejlett szűrési opciókkal.

## Adatmodell

### Táblák

#### `contacts` tábla

- `id` - Elsődleges kulcs
- `name` - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> neve
- `email` - Email cím (egyedi)
- `username` - Felhasználónév
- `org_unit_id` - Szervezeti egység ID (nullable, foreign key)
- `garage_id` - Garázs ID (nullable, foreign key)
- `created_at`, `updated_at` - Időbélyegek

#### `org_units` tábla (Szervezeti egységek)

- `id` - Elsődleges kulcs
- `name` - Szervezeti egység neve
- `created_at`, `updated_at` - Időbélyegek

#### `garages` tábla (Garázsok)

- `id` - <PERSON>sődleges kulcs
- `name` - Garázs neve
- `postal_code` - Irányítószám
- `city` - Város
- `address` - Cím
- `created_at`, `updated_at` - Időbélyegek

### Kapcsolatok

- `Contact` belongsTo `OrgUnit`
- `Contact` belongsTo `Garage`
- `OrgUnit` hasMany `Contact`
- `Garage` hasMany `Contact`

## Funkciók

### 1. 📋 Listázás és szűrés

**Útvonal:** `/admin/contacts`

**Funkciók:**

- Táblázatos megjelenítés
- Keresés név, email, felhasználónév alapján
- Szűrés szervezeti egység szerint
- Szűrés garázs szerint
- Lapozás (15 elem/oldal)
- Rendezés

**Oszlopok:**

- Név
- Email
- Felhasználónév
- Szervezeti egység
- Garázs
- Műveletek (Szerkesztés, Törlés)

### 2. ➕ Új kapcsolattartó létrehozása

**Útvonal:** `/admin/contacts/create`

**Mezők:**

- Név (kötelező)
- Email cím (kötelező, egyedi)
- Felhasználónév (kötelező)
- Szervezeti egység (opcionális, dropdown)
- Garázs (opcionális, dropdown)

**Validáció:**

- Név: kötelező, max 255 karakter
- Email: kötelező, érvényes email formátum, egyedi
- Felhasználónév: kötelező, max 255 karakter
- Szervezeti egység: létező ID vagy null
- Garázs: létező ID vagy null

### 3. ✏️ Kapcsolattartó szerkesztése

**Útvonal:** `/admin/contacts/{id}/edit`

Ugyanazok a mezők és validációs szabályok, mint az új létrehozásnál, kivéve az email egyediség ellenőrzése (saját rekord kizárásával).

### 4. 🗑️ Kapcsolattartó törlése

**Útvonal:** `DELETE /admin/contacts/{id}`

- Megerősítő dialógus
- Végleges törlés (hard delete)

### 5. 📤 Excel Export

**Útvonal:** `GET /admin/contacts/export`

**Exportált oszlopok:**

- Név
- Email
- Felhasználónév
- Szervezeti egység neve
- Garázs neve

**Fájlnév formátum:** `kapcsolattartok_YYYY-MM-DD_HH-mm-ss.xlsx`

### 6. 📥 Excel Import

**Útvonal:** `POST /admin/contacts/import`

**Várt oszlopok:**

1. Név
2. Email
3. Felhasználónév
4. Szervezeti egység neve
5. Garázs neve

**Import logika:**

- Létező email címek kihagyása
- Szervezeti egység és garázs név alapján automatikus kapcsolás
- Hibajelentés sikertelen importnál
- Összesítő jelentés (importált/kihagyott rekordok száma)

## Technikai implementáció

### Backend (Laravel)

#### Controller: `App\Http\Controllers\Admin\ContactController`

- `index()` - Lista megjelenítése szűrésekkel
- `create()` - Új form megjelenítése
- `store()` - Új rekord mentése
- `edit()` - Szerkesztő form megjelenítése
- `update()` - Rekord frissítése
- `destroy()` - Rekord törlése
- `export()` - Excel export
- `import()` - Excel import

#### Models:

- `App\Models\Contact` - Kapcsolattartó model
- `App\Models\OrgUnit` - Szervezeti egység model
- `App\Models\Garage` - Garázs model

#### Request validáció:

- `App\Http\Requests\ContactRequest` - Form validáció

#### Route-ok:

```php
Route::get('admin/contacts', [ContactController::class, 'index']);
Route::post('admin/contacts', [ContactController::class, 'store']);
Route::put('admin/contacts/{contact}', [ContactController::class, 'update']);
Route::delete('admin/contacts/{contact}', [ContactController::class, 'destroy']);
Route::get('admin/contacts/export', [ContactController::class, 'export']);
Route::post('admin/contacts/import', [ContactController::class, 'import']);
```

### Frontend (React + Inertia.js)

#### Komponensek:

- `resources/js/pages/admin/contacts/index.tsx` - Lista oldal modal form-mal
- `resources/js/components/admin/ContactForm.tsx` - Újrafelhasználható form komponens

#### Használt UI komponensek:

- `DataTable` - Táblázatos megjelenítés
- `Select` - Dropdown menük
- `Input` - Szövegmezők
- `Button` - Gombok
- `Dialog` - Import dialógus
- `AlertDialog` - Törlés megerősítés

### Adatbázis

#### Migration-ök:

- `2025_07_15_000001_create_org_units_table.php`
- `2025_07_15_000002_create_garages_table.php`
- `2025_07_15_000003_create_contacts_table.php`

## Jogosultságok

- **Hozzáférés:** Csak `SystemAdmin` jogosultsággal rendelkező felhasználók
- **Menü:** Admin oldalsáv "Kapcsolattartók" menüpont
- **Middleware:** `auth`, `permission:access_system_admin_features`

## Használat

1. **Navigáció:** Admin menü → Kapcsolattartók
2. **Új felvétel:** "Új kapcsolattartó" gomb
3. **Szerkesztés:** Táblázatban "Szerkesztés" ikon
4. **Törlés:** Táblázatban "Törlés" ikon → megerősítés
5. **Export:** "Export" gomb → Excel fájl letöltése
6. **Import:** "Import" gomb → fájl kiválasztása → feltöltés

## Jövőbeli fejlesztési lehetőségek

1. **Tömeges műveletek:** Több rekord egyidejű törlése/módosítása
2. **Részletes nézet:** Kapcsolattartó részletes adatlapja
3. **Kapcsolattartó csoportok:** Kategorizálás és csoportosítás
4. **Email integráció:** Közvetlen email küldés
5. **Audit log:** Változások naplózása
6. **API végpontok:** REST API külső integrációkhoz
7. **Képek:** Profilképek feltöltése és kezelése
