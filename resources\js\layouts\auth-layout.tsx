import AuthLayoutTemplate from '@/layouts/auth/auth-card-layout';

export default function AuthLayout({
    children,
    title,
    description,
    two_column,
    ...props
}: {
    children: React.ReactNode;
    title: string;
    description: string;
    two_column?: boolean;
}) {
    return (
        <AuthLayoutTemplate title={title} description={description} two_column={two_column} {...props}>
            {children}
        </AuthLayoutTemplate>
    );
}
