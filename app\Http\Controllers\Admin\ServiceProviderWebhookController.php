<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ServiceProvider;
use App\Models\ServiceProviderWebhook;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ServiceProviderWebhookController extends Controller
{
    /**
     * Webhook lista egy szervizhez
     */
    public function index(ServiceProvider $serviceProvider)
    {
        $webhooks = $serviceProvider->webhooks()
            ->orderBy('name')
            ->get();

        return Inertia::render('admin/service-providers/webhooks/index', [
            'serviceProvider' => $serviceProvider,
            'webhooks' => $webhooks,
            'httpMethods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
        ]);
    }

    /**
     * Új webhook form
     */
    public function create(ServiceProvider $serviceProvider)
    {
        return Inertia::render('admin/service-providers/webhooks/create', [
            'serviceProvider' => $serviceProvider,
            'eventTypes' => $this->getEventTypes(),
            'httpMethods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
        ]);
    }

    /**
     * Webhook mentése
     */
    public function store(Request $request, ServiceProvider $serviceProvider)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'method' => 'required|in:GET,POST,PUT,PATCH,DELETE',
            'headers' => 'nullable|array',
            'payload_template' => 'nullable|array',
            'secret' => 'nullable|string|max:255',
            'timeout' => 'integer|min:1|max:300',
            'retry_attempts' => 'integer|min:0|max:10',
            'is_active' => 'boolean',
        ]);

        $validated['service_provider_id'] = $serviceProvider->id;

        ServiceProviderWebhook::create($validated);

        return redirect()
            ->route('admin.service-providers.webhooks.index', $serviceProvider)
            ->with('success', 'Webhook sikeresen létrehozva!');
    }

    /**
     * Webhook szerkesztése
     */
    public function edit(ServiceProvider $serviceProvider, ServiceProviderWebhook $webhook)
    {
        return Inertia::render('admin/service-providers/webhooks/edit', [
            'serviceProvider' => $serviceProvider,
            'webhook' => $webhook,
            'eventTypes' => $this->getEventTypes(),
            'httpMethods' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'],
        ]);
    }

    /**
     * Webhook frissítése
     */
    public function update(Request $request, ServiceProvider $serviceProvider, ServiceProviderWebhook $webhook)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'url' => 'required|url|max:500',
            'method' => 'required|in:GET,POST,PUT,PATCH,DELETE',
            'headers' => 'nullable|array',
            'payload_template' => 'nullable|array',
            'secret' => 'nullable|string|max:255',
            'timeout' => 'integer|min:1|max:300',
            'retry_attempts' => 'integer|min:0|max:10',
            'is_active' => 'boolean',
        ]);

        $webhook->update($validated);

        return redirect()
            ->route('admin.service-providers.webhooks.index', $serviceProvider)
            ->with('success', 'Webhook sikeresen frissítve!');
    }

    /**
     * Webhook törlése
     */
    public function destroy(ServiceProvider $serviceProvider, ServiceProviderWebhook $webhook)
    {
        $webhook->delete();

        return redirect()
            ->route('admin.service-providers.webhooks.index', $serviceProvider)
            ->with('success', 'Webhook sikeresen törölve!');
    }

    /**
     * Webhook tesztelése
     */
    public function test(ServiceProvider $serviceProvider, ServiceProviderWebhook $webhook)
    {
        $testPayload = [
            'test' => true,
            'timestamp' => now()->toISOString(),
            'service_provider' => [
                'id' => $serviceProvider->id,
                'name' => $serviceProvider->name,
            ],
            'event_type' => $webhook->event_type,
        ];

        $result = $webhook->call($testPayload);

        return response()->json([
            'success' => $result['success'] ?? false,
            'message' => $result['success'] ?? false 
                ? 'Webhook teszt sikeres!' 
                : 'Webhook teszt sikertelen!',
            'details' => $result,
        ]);
    }

    /**
     * Webhook aktiválás/deaktiválás
     */
    public function toggle(ServiceProvider $serviceProvider, ServiceProviderWebhook $webhook)
    {
        $webhook->update(['is_active' => !$webhook->is_active]);

        $status = $webhook->is_active ? 'aktiválva' : 'deaktiválva';

        return redirect()
            ->route('admin.service-providers.webhooks.index', $serviceProvider)
            ->with('success', "Webhook sikeresen {$status}!");
    }


}
