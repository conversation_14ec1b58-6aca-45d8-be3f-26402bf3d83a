import { usePage } from '@inertiajs/react';

type Translations = Record<string, any>;

export default function useTranslations(namespace?: string) {
    const { props } = usePage();
    const translations: Translations = props.translations || {};

    const t = (key: string, fallback?: string): string => {
        const keys = namespace ? `${namespace}.${key}`.split('.') : key.split('.');
        return keys.reduce((acc: any, k) => acc?.[k], translations) ?? fallback ?? key;
    };

    return { t };
}
