import * as React from 'react';
import { AlertTriangle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  getCategoryInfo,
  getPermissionDisplayName,
  getPermissionDescription,
  type Permission,
} from '@/utils/permissions';

import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// ------------------------------------
// Props
// ------------------------------------

interface SortablePermissionItemProps {
  permission: Permission;
  isSelected: boolean;
  onToggle: (name: string) => void;
  list: 'available' | 'assigned';
  index: number;
  dndActive: boolean;
  disabled: boolean;
  onKeyDown: (
    e: React.KeyboardEvent<HTMLDivElement>,
    name: string,
    list: 'available' | 'assigned'
  ) => void;
}

// ------------------------------------
// Component
// ------------------------------------

export function SortablePermissionItem({
  permission,
  isSelected,
  onToggle,
  list,
  index,
  dndActive,
  disabled,
  onKeyDown,
}: SortablePermissionItemProps) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } =
    useSortable({ id: permission.name, disabled: !dndActive });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : undefined,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      aria-selected={isSelected}
      data-name={permission.name}
      data-index={index}
      data-list={list}
      onKeyDown={(e) => onKeyDown(e, permission.name, list)}
      className={`p-3 border rounded-lg transition-colors cursor-pointer select-none
        ${isSelected ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'}
        focus:outline-none focus:ring-2 focus:ring-blue-400`}
      onClick={() => onToggle(permission.name)}
      {...attributes}
      {...listeners}
    >
      <div className="flex items-start gap-3">
        <Checkbox
          checked={isSelected}
          onCheckedChange={() => onToggle(permission.name)}
          disabled={disabled}
          onClick={(e) => e.stopPropagation()}
        />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{getPermissionDisplayName(permission)}</span>
            {!!permission.is_dangerous && <AlertTriangle className="h-4 w-4 text-amber-500" />}
          </div>
          <p className="text-xs text-gray-600 mb-2">{getPermissionDescription(permission)}</p>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {getCategoryInfo(permission.category)?.name || 'Általános'}
            </Badge>
            <code className="text-xs text-gray-500 bg-gray-100 px-1 rounded">{permission.name}</code>
          </div>
        </div>
      </div>
    </div>
  );
}