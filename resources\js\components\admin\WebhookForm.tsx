import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SingleSelect } from '@/components/ui/single-select';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from '@inertiajs/react';
import { Clock, Code, Globe, RotateCcw, Settings, Shield } from 'lucide-react';
import { FormEventHandler, useEffect, useState } from 'react';

interface WebhookFormProps {
    serviceProvider: {
        id: number;
        name: string;
    };
    webhook?: {
        id?: number;
        name: string;
        url: string;
        method: string;
        headers?: Record<string, string>;
        payload_template?: Record<string, any>;
        secret?: string;
        timeout: number;
        retry_attempts: number;
        is_active: boolean;
    } | null;
    httpMethods: string[];
    onCancel: () => void;
}

export default function WebhookForm({ serviceProvider, webhook, httpMethods, onCancel }: WebhookFormProps) {
    const [headersText, setHeadersText] = useState(webhook?.headers ? JSON.stringify(webhook.headers, null, 2) : '');
    const [payloadText, setPayloadText] = useState(webhook?.payload_template ? JSON.stringify(webhook.payload_template, null, 2) : '');
    // Helyi állapot a JSON validációs hibáknak
    const [headersError, setHeadersError] = useState<string | null>(null);
    const [payloadError, setPayloadError] = useState<string | null>(null);

    const { data, setData, post, put, processing, errors, wasSuccessful } = useForm({
        name: webhook?.name || '',
        url: webhook?.url || '',
        method: webhook?.method || 'POST',
        headers: webhook?.headers || {},
        payload_template: webhook?.payload_template || {},
        secret: webhook?.secret || '',
        timeout: webhook?.timeout || 30,
        retry_attempts: webhook?.retry_attempts || 3,
        is_active: webhook?.is_active ?? true,
    });

    useEffect(() => {
        if (wasSuccessful) {
            onCancel();
        }
    }, [wasSuccessful, onCancel]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();

        // Korábbi hibák törlése
        setHeadersError(null);
        setPayloadError(null);

        // JSON parsing for headers and payload
        let parsedHeaders = {};
        let parsedPayload = {};

        try {
            if (headersText.trim()) {
                parsedHeaders = JSON.parse(headersText);
            }
        } catch (error) {
            setHeadersError('Hibás JSON formátum. Kérjük, ellenőrizze a szintaxist.');
            return;
        }

        try {
            if (payloadText.trim()) {
                parsedPayload = JSON.parse(payloadText);
            }
        } catch (error) {
            setPayloadError('Hibás JSON formátum. Kérjük, ellenőrizze a szintaxist.');
            return;
        }

        // Manuálisan frissítjük az adatokat
        const submitData = {
            ...data,
            headers: parsedHeaders,
            payload_template: parsedPayload,
        };

        if (webhook?.id) {
            put(route('admin.service-providers.webhooks.update', [serviceProvider.id, webhook.id]), submitData as any);
        } else {
            post(route('admin.service-providers.webhooks.store', serviceProvider.id), submitData as any);
        }
    };

    const getDefaultPayloadTemplate = () => {
        const template = {
            timestamp: '{{timestamp}}',
            service_provider: {
                id: '{{service_provider.id}}',
                name: '{{service_provider.name}}',
            },
            data: '{{data}}',
        };
        setPayloadText(JSON.stringify(template, null, 2));
    };

    return (
        <form onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
            <div className="flex-1 space-y-6 overflow-y-auto p-6">
                {/* Alapadatok */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium text-gray-900">Alapadatok</h3>

                    <div className="space-y-2">
                        <Label htmlFor="name">Webhook neve *</Label>
                        <Input
                            id="name"
                            type="text"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing}
                            placeholder="pl. Új javítási igény webhook"
                            className={errors.name ? 'border-red-500' : ''}
                        />
                        {errors.name && <div className="text-sm text-red-500">{errors.name}</div>}
                    </div>
                </div>

                {/* HTTP beállítások */}
                <div className="space-y-4">
                    <h3 className="text-md flex items-center gap-2 font-medium text-gray-900">
                        <Globe className="h-4 w-4" />
                        HTTP beállítások
                    </h3>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-5">
                        <div className="space-y-2">
                            <SingleSelect
                                label="HTTP metódus *"
                                value={data.method}
                                onChange={(value) => setData('method', value as string)}
                                options={httpMethods.map((method) => ({
                                    value: method,
                                    label: method,
                                }))}
                                placeholder="Válassz metódust"
                            />
                            {errors.method && <div className="text-sm text-red-500">{errors.method}</div>}
                        </div>

                        <div className="space-y-2 md:col-span-4">
                            <Label htmlFor="url">Webhook URL *</Label>
                            <Input
                                id="url"
                                type="url"
                                value={data.url}
                                onChange={(e) => setData('url', e.target.value)}
                                disabled={processing}
                                placeholder="https://example.com/webhook"
                                className={errors.url ? 'border-red-500' : ''}
                            />
                            {errors.url && <div className="text-sm text-red-500">{errors.url}</div>}
                        </div>
                    </div>
                </div>

                {/* Fejlesztett beállítások */}
                <div className="space-y-4">
                    <h3 className="text-md flex items-center gap-2 font-medium text-gray-900">
                        <Settings className="h-4 w-4" />
                        Fejlesztett beállítások
                    </h3>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                            <Label htmlFor="timeout" className="flex items-center gap-1">
                                <Clock className="h-4 w-4" />
                                Timeout (másodperc)
                            </Label>
                            <Input
                                id="timeout"
                                type="number"
                                min="1"
                                max="300"
                                value={data.timeout}
                                onChange={(e) => setData('timeout', parseInt(e.target.value) || 30)}
                                disabled={processing}
                                className={errors.timeout ? 'border-red-500' : ''}
                            />
                            {errors.timeout && <div className="text-sm text-red-500">{errors.timeout}</div>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="retry_attempts" className="flex items-center gap-1">
                                <RotateCcw className="h-4 w-4" />
                                Újrapróbálkozások
                            </Label>
                            <Input
                                id="retry_attempts"
                                type="number"
                                min="0"
                                max="10"
                                value={data.retry_attempts}
                                onChange={(e) => setData('retry_attempts', parseInt(e.target.value) || 3)}
                                disabled={processing}
                                className={errors.retry_attempts ? 'border-red-500' : ''}
                            />
                            {errors.retry_attempts && <div className="text-sm text-red-500">{errors.retry_attempts}</div>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="secret" className="flex items-center gap-1">
                                <Shield className="h-4 w-4" />
                                Webhook Secret
                            </Label>
                            <Input
                                id="secret"
                                type="password"
                                value={data.secret}
                                onChange={(e) => setData('secret', e.target.value)}
                                disabled={processing}
                                placeholder="Opcionális biztonsági kulcs"
                                className={errors.secret ? 'border-red-500' : ''}
                            />
                            {errors.secret && <div className="text-sm text-red-500">{errors.secret}</div>}
                        </div>
                    </div>
                </div>

                {/* JSON beállítások */}
                <div className="space-y-4">
                    <h3 className="text-md flex items-center gap-2 font-medium text-gray-900">
                        <Code className="h-4 w-4" />
                        JSON beállítások
                    </h3>

                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="headers">HTTP Headers (JSON)</Label>
                            <Textarea
                                id="headers"
                                value={headersText}
                                onChange={(e) => setHeadersText(e.target.value)}
                                disabled={processing}
                                placeholder='{"Content-Type": "application/json", "Authorization": "Bearer token"}'
                                rows={5}
                                className={`font-mono text-sm ${headersError ? 'border-red-500' : ''}`}
                            />
                            {headersError && <p className="text-sm text-red-500">{headersError}</p>}
                            <p className="text-xs text-gray-500">Opcionális HTTP headerek JSON formátumban</p>
                        </div>

                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <Label htmlFor="payload_template">Payload Template (JSON)</Label>
                                <Button type="button" variant="outline" size="sm" onClick={getDefaultPayloadTemplate} disabled={processing}>
                                    Alapértelmezett
                                </Button>
                            </div>
                            <Textarea
                                id="payload_template"
                                value={payloadText}
                                onChange={(e) => setPayloadText(e.target.value)}
                                disabled={processing}
                                placeholder='{"data": "{{data}}", "timestamp": "{{timestamp}}"}'
                                rows={6}
                                className={`font-mono text-sm ${payloadError ? 'border-red-500' : ''}`}
                            />
                            {payloadError && <p className="text-sm text-red-500">{payloadError}</p>}
                            <p className="text-xs text-gray-500">Payload sablon változókkal: {`{{variable}}`}</p>
                        </div>
                    </div>
                </div>

                {/* Állapot */}
                <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="is_active"
                            checked={data.is_active}
                            onChange={(e) => setData('is_active', e.target.checked)}
                            disabled={processing}
                            className="rounded border-gray-300"
                        />
                        <Label htmlFor="is_active" className="cursor-pointer">
                            Aktív webhook
                        </Label>
                    </div>
                    {!data.is_active && (
                        <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
                            <p className="text-sm text-amber-700">⚠️ Inaktív webhookok nem kerülnek meghívásra események esetén.</p>
                        </div>
                    )}
                </div>
            </div>

            {/* Gombok */}
            <DialogFooter className="flex-shrink-0 border-t p-6">
                <Button type="button" variant="outline" onClick={onCancel} disabled={processing}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {processing ? 'Mentés...' : webhook?.id ? 'Mentés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </form>
    );
}
