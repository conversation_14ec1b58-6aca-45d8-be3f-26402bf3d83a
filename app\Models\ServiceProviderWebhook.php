<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ServiceProviderWebhook extends Model
{
    use HasFactory;

    protected $fillable = [
        'service_provider_id',
        'name',
        'event_type',
        'url',
        'method',
        'headers',
        'payload_template',
        'secret',
        'is_active',
        'timeout',
        'retry_attempts',
        'last_called_at',
        'last_status',
        'last_response',
    ];

    protected $casts = [
        'headers' => 'array',
        'payload_template' => 'array',
        'is_active' => 'boolean',
        'last_called_at' => 'datetime',
    ];

    /**
     * Szerviz kapcsolat
     */
    public function serviceProvider(): BelongsTo
    {
        return $this->belongsTo(ServiceProvider::class);
    }

    /**
     * Webhook hívás végrehajtása
     */
    public function call(array $payload = []): array
    {
        $startTime = now();
        
        try {
            // Payload előkészítése template alapján
            $finalPayload = $this->preparePayload($payload);
            
            // HTTP kérés előkészítése
            $http = Http::timeout($this->timeout);
            
            // Headers hozzáadása
            if ($this->headers) {
                $http = $http->withHeaders($this->headers);
            }
            
            // Secret hozzáadása ha van
            if ($this->secret) {
                $signature = hash_hmac('sha256', json_encode($finalPayload), $this->secret);
                $http = $http->withHeaders(['X-Webhook-Signature' => $signature]);
            }
            
            // HTTP kérés végrehajtása
            $response = match (strtoupper($this->method)) {
                'GET' => $http->get($this->url, $finalPayload),
                'POST' => $http->post($this->url, $finalPayload),
                'PUT' => $http->put($this->url, $finalPayload),
                'PATCH' => $http->patch($this->url, $finalPayload),
                'DELETE' => $http->delete($this->url, $finalPayload),
                default => $http->post($this->url, $finalPayload),
            };
            
            $status = $response->successful() ? 'success' : 'failed';
            $responseBody = $response->body();
            
            // Eredmény mentése
            $this->update([
                'last_called_at' => $startTime,
                'last_status' => $status,
                'last_response' => $responseBody,
            ]);
            
            Log::info("Webhook called successfully", [
                'webhook_id' => $this->id,
                'service_provider' => $this->serviceProvider->name,
                'event_type' => $this->event_type,
                'status' => $status,
                'response_time' => now()->diffInMilliseconds($startTime) . 'ms'
            ]);
            
            return [
                'success' => $response->successful(),
                'status_code' => $response->status(),
                'response' => $responseBody,
                'response_time' => now()->diffInMilliseconds($startTime),
            ];
            
        } catch (\Exception $e) {
            // Hiba esetén
            $this->update([
                'last_called_at' => $startTime,
                'last_status' => 'error',
                'last_response' => $e->getMessage(),
            ]);
            
            Log::error("Webhook call failed", [
                'webhook_id' => $this->id,
                'service_provider' => $this->serviceProvider->name,
                'event_type' => $this->event_type,
                'error' => $e->getMessage(),
            ]);
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'response_time' => now()->diffInMilliseconds($startTime),
            ];
        }
    }

    /**
     * Payload előkészítése template alapján
     */
    private function preparePayload(array $data): array
    {
        if (!$this->payload_template) {
            return $data;
        }
        
        $template = $this->payload_template;
        
        // Template változók helyettesítése
        return $this->replaceTemplateVariables($template, $data);
    }

    /**
     * Template változók helyettesítése
     */
    private function replaceTemplateVariables($template, array $data)
    {
        if (is_array($template)) {
            $result = [];
            foreach ($template as $key => $value) {
                $result[$key] = $this->replaceTemplateVariables($value, $data);
            }
            return $result;
        }
        
        if (is_string($template)) {
            // {{variable}} formátumú változók helyettesítése
            return preg_replace_callback('/\{\{(\w+(?:\.\w+)*)\}\}/', function ($matches) use ($data) {
                $path = explode('.', $matches[1]);
                $value = $data;
                
                foreach ($path as $key) {
                    if (is_array($value) && isset($value[$key])) {
                        $value = $value[$key];
                    } else {
                        return $matches[0]; // Változó nem található, eredeti marad
                    }
                }
                
                return $value;
            }, $template);
        }
        
        return $template;
    }

    /**
     * Aktív webhookok scope
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Esemény típus szerint scope
     */
    public function scopeForEvent($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }
}
