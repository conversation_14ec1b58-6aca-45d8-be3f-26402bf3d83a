import InputError from '@/components/input-error';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ReactNode } from 'react';

interface FormInputProps {
    id: string;
    name: string;
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    placeholder?: string;
    type?: string;
    autoFocus?: boolean;
    autoComplete?: string;
    required?: boolean;
    tabIndex?: number;
    icon?: ReactNode;
    readOnly?: boolean;
}

export default function FormInput({
    id,
    name,
    label,
    value,
    onChange,
    error,
    placeholder,
    type = 'text',
    autoFocus = false,
    autoComplete,
    required = false,
    tabIndex,
    icon,
    readOnly = false,
}: FormInputProps) {
    return (
        <div className="grid gap-2">
            <Label htmlFor={id}>{label}</Label>
            <div className="relative">
                {icon && <div className="absolute top-3 left-3 h-4 w-4 text-gray-400">{icon}</div>}
                <Input
                    id={id}
                    name={name}
                    type={type}
                    autoComplete={autoComplete}
                    value={value}
                    onChange={onChange}
                    placeholder={placeholder}
                    autoFocus={autoFocus}
                    required={required}
                    tabIndex={tabIndex}
                    readOnly={readOnly}
                    className={icon ? 'pl-10' : ''}
                />
            </div>
            {error && <InputError message={error} className="mt-2" />}
        </div>
    );
}
