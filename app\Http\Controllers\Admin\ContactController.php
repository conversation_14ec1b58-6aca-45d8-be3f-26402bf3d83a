<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\ContactRequest;
use App\Models\Contact;
use App\Models\OrgUnit;
use App\Models\Garage;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ContactImport;
use Maatwebsite\Excel\Validators\ValidationException;
use Illuminate\Http\JsonResponse;

class ContactController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): Response
    {
        $search = $request->get('search');
        $orgUnitFilter = $request->get('org_unit');
        $garageFilter = $request->get('garage');

        $contacts = Contact::with(['orgUnit', 'garage'])
            ->when($search, function ($query, $search) {
                return $query->search($search);
            })
            ->when($orgUnitFilter, function ($query, $orgUnitFilter) {
                return $query->where('org_unit_id', $orgUnitFilter);
            })
            ->when($garageFilter, function ($query, $garageFilter) {
                return $query->where('garage_id', $garageFilter);
            })
            ->orderBy('name')
            ->paginate(15)
            ->withQueryString();

        $orgUnits = OrgUnit::orderBy('name')->get();
        $garages = Garage::orderBy('name')->get();

        return Inertia::render('admin/contacts/index', [
            'contacts' => $contacts->items(),
            'orgUnits' => $orgUnits,
            'garages' => $garages,
            'filters' => [
                'search' => $search,
                'org_unit' => $orgUnitFilter,
                'garage' => $garageFilter,
            ],
        ]);
    }



    /**
     * Store a newly created resource in storage.
     */
    public function store(ContactRequest $request): RedirectResponse
    {
        $data = $request->validated();

        // Convert empty strings to null for foreign keys
        $data['org_unit_id'] = $data['org_unit_id'] ?: null;
        $data['garage_id'] = $data['garage_id'] ?: null;

        Contact::create($data);

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Kapcsolattartó sikeresen létrehozva.');
    }



    /**
     * Update the specified resource in storage.
     */
    public function update(ContactRequest $request, Contact $contact): RedirectResponse
    {
        $data = $request->validated();

        // Convert empty strings to null for foreign keys
        $data['org_unit_id'] = $data['org_unit_id'] ?: null;
        $data['garage_id'] = $data['garage_id'] ?: null;

        $contact->update($data);

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Kapcsolattartó sikeresen frissítve.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contact $contact): RedirectResponse
    {
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'Kapcsolattartó sikeresen törölve.');
    }

    /**
     * Export contacts to Excel.
     */
    public function export(): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $contacts = Contact::with(['orgUnit', 'garage'])->orderBy('name')->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Headers
        $sheet->setCellValue('A1', 'Név');
        $sheet->setCellValue('B1', 'Email');
        $sheet->setCellValue('C1', 'Felhasználónév');
        $sheet->setCellValue('D1', 'Szervezeti egység');
        $sheet->setCellValue('E1', 'Garázs');

        // Data
        $row = 2;
        foreach ($contacts as $contact) {
            $sheet->setCellValue('A' . $row, $contact->name);
            $sheet->setCellValue('B' . $row, $contact->email);
            $sheet->setCellValue('C' . $row, $contact->username);
            $sheet->setCellValue('D' . $row, $contact->orgUnit?->name ?? '');
            $sheet->setCellValue('E' . $row, $contact->garage?->name ?? '');
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'kapcsolattartok_' . date('Y-m-d_H-i-s') . '.xlsx';
        $tempFile = tempnam(sys_get_temp_dir(), $filename);
        $writer->save($tempFile);

        return response()->download($tempFile, $filename)->deleteFileAfterSend();
    }

    /**
     * Import contacts from Excel.
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240',
        ]);

        try {
            Excel::import(new ContactImport(), $request->file('file'));

            return redirect()
                ->route('admin.contacts.index')
                ->with('success', 'Kapcsolattartók sikeresen importálva!');
        } catch (ValidationException $e) {
            $failures = $e->failures();
            $errors = [];
            foreach ($failures as $failure) {
                $errors[] = 'Sor: ' . $failure->row() . ' - ' . implode(', ', $failure->errors());
            }
            return redirect()
                ->route('admin.contacts.index')
                ->with('import_errors', $errors);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.contacts.index')
                ->with('error', 'Hiba történt az importálás során: ' . $e->getMessage());
        }
    }

    /**
     * Download the sample import file.
     */
    public function downloadSample()
    {
        $path = storage_path('app/samples/kapcsolattartok_minta.csv');

        if (!file_exists($path)) {
            abort(404, 'A minta fájl nem található.');
        }

        return response()->download($path, 'kapcsolattartok_minta.csv');
    }
}
