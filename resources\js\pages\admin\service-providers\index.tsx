import { Head, Link, router } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Building2, Download, Mail, MapPin, Phone, Search, Settings, Upload, Webhook } from 'lucide-react';
import { useState } from 'react';

import ServiceProviderForm from '@/components/admin/ServiceProviderForm';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { SingleSelect } from '@/components/ui/single-select';
import AppLayout from '@/layouts/app-layout';

interface ServiceProvider {
    id: number;
    name: string;
    postal_code: string;
    street: string;
    city: string;
    address: string;
    phone?: string;
    emails?: string[];
    is_active: boolean;
    webhooks_count: number;
    created_at: string;
    updated_at: string;
}

interface Props {
    serviceProviders: {
        data: ServiceProvider[];
        links: any[];
        meta: any;
    };
    filters: {
        search?: string;
        status?: string;
    };
}

export default function ServiceProvidersIndex({ serviceProviders, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || '');
    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<ServiceProvider | null>(null);

    const handleSearch = () => {
        router.get(
            route('admin.service-providers.index'),
            {
                search: search || undefined,
                status: status || undefined,
            },
            {
                preserveState: true,
                replace: true,
            },
        );
    };

    const handleReset = () => {
        setSearch('');
        setStatus('');
        router.get(route('admin.service-providers.index'));
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const handleEdit = (serviceProvider: ServiceProvider) => {
        setEditing(serviceProvider);
        setOpen(true);
    };

    const confirmDelete = (serviceProvider: ServiceProvider) => {
        if (confirm('Biztosan törli ezt a szervizet?')) {
            router.delete(route('admin.service-providers.destroy', serviceProvider.id));
        }
    };

    const columns: ColumnDef<ServiceProvider>[] = [
        {
            header: 'Szerviz neve',
            accessorKey: 'name',
            cell: ({ row }) => (
                <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-gray-400" />
                    <div>
                        <div className="font-medium">{row.original.name}</div>
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                            <MapPin className="h-3 w-3" />
                            {row.original.city}
                        </div>
                    </div>
                </div>
            ),
        },
        {
            header: 'Cím',
            accessorKey: 'address',
            cell: ({ row }) => (
                <div className="text-sm">
                    <div>
                        {row.original.postal_code} {row.original.city}
                    </div>
                    <div className="text-gray-500">{row.original.street}</div>
                </div>
            ),
        },
        {
            header: 'Elérhetőség',
            cell: ({ row }) => (
                <div className="space-y-1">
                    {row.original.phone && (
                        <div className="flex items-center gap-1 text-sm">
                            <Phone className="h-3 w-3 text-gray-400" />
                            {row.original.phone}
                        </div>
                    )}
                    {row.original.emails && row.original.emails.length > 0 && (
                        <div className="flex items-center gap-1 text-sm">
                            <Mail className="h-3 w-3 text-gray-400" />
                            {row.original.emails[0]}
                            {row.original.emails.length > 1 && <span className="text-gray-400">+{row.original.emails.length - 1}</span>}
                        </div>
                    )}
                </div>
            ),
        },
        {
            header: 'Webhookok',
            cell: ({ row }) => (
                <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2">
                        <Webhook className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{row.original.webhooks_count}</span>
                    </div>
                    <Button asChild variant="ghost" size="icon" className="h-8 w-8 shrink-0">
                        <Link href={route('admin.service-providers.webhooks.index', row.original.id)} title="Webhookok kezelése">
                            <Settings className="h-4 w-4" />
                        </Link>
                    </Button>
                </div>
            ),
        },
        {
            header: 'Állapot',
            accessorKey: 'is_active',
            cell: ({ row }) => (
                <Badge variant={row.original.is_active ? 'default' : 'secondary'}>{row.original.is_active ? 'Aktív' : 'Inaktív'}</Badge>
            ),
        },
    ];

    return (
        <AppLayout>
            <Head title="Szervizek kezelése" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Szervizek kezelése</h1>
                        <p className="text-gray-600">Javítószervizek és webhook beállításaik kezelése</p>
                    </div>
                    <div className="flex items-center gap-2">
                        <Button variant="outline" size="sm">
                            <Download className="mr-2 h-4 w-4" />
                            Export
                        </Button>
                        <Button variant="outline" size="sm">
                            <Upload className="mr-2 h-4 w-4" />
                            Import
                        </Button>
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                    + Új szerviz
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="flex max-h-[90vh] max-w-4xl flex-col p-0"
                                onInteractOutside={(e) => {
                                    e.preventDefault();
                                }}
                            >
                                <DialogHeader className="p-6 pb-0">
                                    <DialogTitle>{editing ? 'Szerviz szerkesztése' : 'Új szerviz'}</DialogTitle>
                                    <DialogDescription>Töltsd ki az alábbi űrlapot a szerviz adatainak mentéséhez.</DialogDescription>
                                </DialogHeader>

                                <ServiceProviderForm serviceProvider={editing} onCancel={() => setOpen(false)} />
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                {/* Filters */}
                <div className="space-y-4 rounded-lg border bg-white p-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Keresés</label>
                            <div className="relative">
                                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                <Input
                                    placeholder="Szerviz neve, város, utca..."
                                    value={search}
                                    onChange={(e) => setSearch(e.target.value)}
                                    className="pl-10"
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <SingleSelect
                                label="Állapot"
                                value={status}
                                onChange={(value) => setStatus(value as string)}
                                options={[
                                    { value: '', label: 'Minden állapot' },
                                    { value: 'active', label: 'Aktív' },
                                    { value: 'inactive', label: 'Inaktív' },
                                ]}
                                placeholder="Minden állapot"
                            />
                        </div>
                        <div className="flex items-end gap-2">
                            <Button onClick={handleSearch} className="flex-1">
                                Szűrés
                            </Button>
                            <Button variant="outline" onClick={handleReset}>
                                Törlés
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Table */}
                <div className="rounded-lg border bg-white">
                    <DataTable
                        columns={columns}
                        data={serviceProviders.data}
                        onEdit={handleEdit}
                        onDelete={confirmDelete}
                        globalFilter={search}
                        exportFileName="szervizek"
                        pageSize={10}
                        showExportButton={false}
                    />
                </div>
            </div>
        </AppLayout>
    );
}
