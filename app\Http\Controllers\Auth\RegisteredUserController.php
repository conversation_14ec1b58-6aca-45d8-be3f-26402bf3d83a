<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Contact;
use App\Models\OrgUnit;
use App\Models\Garage;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(): Response
    {
        return Inertia::render('auth/register', [
            'orgUnits' => OrgUnit::orderBy('name')->get(['id', 'name']),
            'garages' => Garage::orderBy('name')->get(['id', 'name']),
            'positions' => User::getExistingPositions(),
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        // Alapvető validáció
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:users,email',
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'company_type' => 'required|in:posta,partner',
        ];

        // Posta specifikus validáció
        if ($request->company_type === 'posta') {
            $rules['email'] .= '|ends_with:@posta.hu';
            $rules['org_unit_id'] = 'required|exists:org_units,id';
            $rules['garage_id'] = 'required|exists:garages,id';
            $rules['username'] = 'required|exists:contacts,username';

            // Ellenőrizzük, hogy az email létezik-e a contacts táblában
            $contact = Contact::where('email', $request->email)->first();
            if (!$contact) {
                return back()->withErrors([
                    'email' => 'A megadott emailcím nem található az adatbázisunkban, az MP Zrt. által megadott adatok között. Kérjük adjon meg egy megfelelő email címet!'
                ]);
            }
        }

        // Partner specifikus validáció
        if ($request->company_type === 'partner') {
            $rules['company_name'] = 'required|string|max:255';
            $rules['tax_number'] = 'required|string|max:20';
            $rules['position'] = 'required|string|max:255';
            $rules['phone'] = 'required|string|max:20';
        }

        $validated = $request->validate($rules);

        // Contact ID meghatározása Posta felhasználókhoz
        $contactId = null;
        if ($request->company_type === 'posta') {
            $contact = Contact::where('email', $request->email)->first();
            $contactId = $contact?->id;
        }

        // User létrehozása - MINDIG INAKTÍV
        $user = User::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'company_type' => $validated['company_type'],
            'company_name' => $validated['company_name'] ?? null,
            'tax_number' => $validated['tax_number'] ?? null,
            'position' => $validated['position'] ?? null,
            'phone' => $validated['phone'] ?? null,
            'is_active' => false, // MINDIG INAKTÍV - Admin jóváhagyás szükséges
            'contact_id' => $contactId,
            'org_unit_id' => $validated['org_unit_id'] ?? null,
            'garage_id' => $validated['garage_id'] ?? null,
        ]);

        event(new Registered($user));

        // NEM jelentkeztetjük be automatikusan - inaktív fiók
        return redirect()->route('login')->with('status',
            'Regisztráció sikeres! A fiókja inaktív, az adminisztrátor jóváhagyása szükséges a bejelentkezéshez.'
        );
    }
}
