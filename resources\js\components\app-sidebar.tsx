import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Building2, Car, Contact, LayoutGrid, LockKeyhole, ShieldCheck, UserCheck, Users, Wrench } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
        icon: LayoutGrid,
    },
];

const adminNavItems: NavItem[] = [
    {
        title: 'Jogosultságok',
        href: '/admin/permissions',
        icon: LockKeyhole,
    },
    {
        title: 'Szerepkör<PERSON><PERSON>',
        href: '/admin/roles',
        icon: Shield<PERSON>he<PERSON>,
    },
    {
        title: 'Cégek',
        href: '/admin/companies',
        icon: Building2,
    },
    {
        title: 'Szervizek',
        href: '/admin/service-providers',
        icon: Wrench,
    },
    {
        title: 'Postai járművek',
        href: '/admin/postal-vehicles',
        icon: Car,
    },
    {
        title: 'Felhasználók',
        href: '/admin/users',
        icon: Users,
    },
    {
        title: 'Kapcsolattartók',
        href: '/admin/contacts',
        icon: Contact,
    },
    {
        title: 'Felhasználó jóváhagyás',
        href: '/admin/user-approval',
        icon: UserCheck,
    },
];

const footerNavItems: NavItem[] = [];

export function AppSidebar() {
    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href="/dashboard" prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain label="Platforms" items={mainNavItems} />
                <NavMain label="Admin" items={adminNavItems} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
