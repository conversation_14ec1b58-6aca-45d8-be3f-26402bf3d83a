<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $contactId = $this->route('contact')?->id;

        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('contacts', 'email')->ignore($contactId),
            ],
            'username' => ['required', 'string', 'max:255'],
            'org_unit_id' => ['nullable', 'exists:org_units,id'],
            'garage_id' => ['nullable', 'exists:garages,id'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'A név megadása kötelező.',
            'email.required' => 'Az email cím megadása kötelező.',
            'email.email' => 'Érvényes email címet adjon meg.',
            'email.unique' => 'Ez az email cím már használatban van.',
            'username.required' => 'A felhasználónév megadása kötelező.',
            'org_unit_id.exists' => 'A kiválasztott szervezeti egység nem létezik.',
            'garage_id.exists' => 'A kiválasztott garázs nem létezik.',
        ];
    }
}
