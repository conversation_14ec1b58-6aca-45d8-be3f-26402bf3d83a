<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ServiceProvider extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'postal_code',
        'street',
        'city',
        'address',
        'phone',
        'emails',
        'is_active',
    ];

    protected $casts = [
        'emails' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Webhook kapcsolatok
     */
    public function webhooks(): HasMany
    {
        return $this->hasMany(ServiceProviderWebhook::class);
    }

    /**
     * Aktív webhookok
     */
    public function activeWebhooks(): HasMany
    {
        return $this->hasMany(ServiceProviderWebhook::class)->where('is_active', true);
    }

    /**
     * Teljes cím generálása
     */
    public function getFullAddressAttribute(): string
    {
        return trim("{$this->postal_code} {$this->city}, {$this->street}");
    }

    /**
     * <PERSON>ső email cím
     */
    public function getPrimaryEmailAttribute(): ?string
    {
        return $this->emails[0] ?? null;
    }

    /**
     * Aktív szervizek scope
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Keresés scope
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('city', 'like', "%{$search}%")
              ->orWhere('street', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%");
        });
    }

    /**
     * Webhook hívás egy adott eseményhez
     */
    public function callWebhooks(string $eventType, array $payload = []): array
    {
        $results = [];
        
        $webhooks = $this->activeWebhooks()
            ->where('event_type', $eventType)
            ->get();

        foreach ($webhooks as $webhook) {
            $results[] = $webhook->call($payload);
        }

        return $results;
    }
}
