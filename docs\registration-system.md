# Új Regisztrációs Rendszer

## Áttekintés

Az új regisztrációs rendszer két típusú felhasználót támogat:
- **Magyar Posta Zrt. alkalmazottak** (@posta.hu email domain)
- **Partner cégek** (egyéb email domain-ek)

Minden regisztrált felhasználó **inaktív** állapotban kerül a rendszerbe, és admin jóváhagyás szükséges az aktiváláshoz.

## Regisztrációs Folyamat

### 1. Cégválasztó
A felhasználó először kiválasztja a cég típusát:
- **Magyar Posta Zrt.**
- **Más partner**

### 2. Posta Alkalmazottak Regisztrációja

#### Kötelező mezők:
- **Név** (automatikusan kitöltődik a contacts táblából)
- **Email cím** (csak @posta.hu domain)
- **Szervezeti egység** (legörd<PERSON><PERSON><PERSON> menü)
- **Posta Garázs** (legördülő menü)
- **Jelszó** + **Jelszó megerősítése**

#### Validáció:
- Email cím **kötelezően @posta.hu** domain-ű
- Email címnek **létezni kell a contacts táblában**
- Ha nem található: "A megadott emailcím nem található az adatbázisunkban..."

#### Automatikus kitöltés:
- Név, szervezeti egység, garázs automatikusan kitöltődik a contacts tábla alapján
- Felhasználó módosíthatja a szervezeti egységet és garázst (legördülő menüből)

### 3. Partner Cégek Regisztrációja

#### Kötelező mezők:
- **Név**
- **Email cím** (bármilyen domain)
- **Cégnév**
- **Adószám**
- **Munkakör** (SingleSelect allowCustomValue-val)
- **Telefonszám**
- **Jelszó** + **Jelszó megerősítése**

## Adatbázis Struktúra

### Users tábla bővítése:
```sql
-- Regisztrációs mezők
company_type ENUM('posta', 'partner') NULL
company_name VARCHAR(255) NULL
tax_number VARCHAR(255) NULL  -- már létezett
position VARCHAR(255) NULL    -- már létezett
phone VARCHAR(255) NULL       -- már létezett

-- Admin jóváhagyás
is_active BOOLEAN DEFAULT FALSE  -- már létezett

-- Kapcsolatok (Posta felhasználókhoz)
contact_id BIGINT UNSIGNED NULL
org_unit_id BIGINT UNSIGNED NULL
garage_id BIGINT UNSIGNED NULL
```

### Kapcsolatok:
- `contact_id` → `contacts.id`
- `org_unit_id` → `org_units.id`
- `garage_id` → `garages.id`

## Backend Implementáció

### RegisteredUserController

#### create() metódus:
```php
return Inertia::render('auth/register', [
    'orgUnits' => OrgUnit::orderBy('name')->get(['id', 'name']),
    'garages' => Garage::orderBy('name')->get(['id', 'name']),
    'positions' => [...] // Előre definiált munkakörök
]);
```

#### store() metódus:
- **Validáció**: Cég típus alapján eltérő szabályok
- **Posta validáció**: @posta.hu domain + contacts tábla ellenőrzés
- **Partner validáció**: Cégnév, adószám, munkakör, telefon kötelező
- **User létrehozás**: `is_active = false` (mindig inaktív)
- **Átirányítás**: Login oldalra üzenettel

### Contact Lookup API

#### Endpoint: `/api/contacts/lookup`
```php
// ContactLookupController@lookup
GET /api/contacts/lookup?email=<EMAIL>

Response:
{
    "success": true,
    "contact": {
        "id": 1,
        "name": "Nagy János",
        "email": "<EMAIL>",
        "org_unit_id": 5,
        "garage_id": 3,
        "org_unit": { "id": 5, "name": "Központi Iroda" },
        "garage": { "id": 3, "name": "Budapest Garázs" }
    }
}
```

### Login Validáció
A `LoginRequest` már ellenőrzi az `is_active` mezőt:
```php
if ($user && !$user->is_active) {
    Auth::logout();
    throw ValidationException::withMessages([
        'email' => 'Ez a fiók inaktív. Kérjük, vedd fel a kapcsolatot az adminisztrátorral.'
    ]);
}
```

## Frontend Implementáció

### Regisztrációs Form (`auth/register.tsx`)

#### Dinamikus mezők:
- **Cégválasztó alapján** jelennek meg a megfelelő mezők
- **Email validáció**: Valós idejű @posta.hu ellenőrzés
- **Contact lookup**: Automatikus API hívás Posta email esetén
- **Feltételes submit**: Posta esetén csak valid contact-tal engedélyezett

#### Használt komponensek:
- `SingleSelect` - Szervezeti egység, garázs, munkakör
- `Select` - Cégválasztó
- `Input` - Szöveges mezők
- `PasswordInput` - Jelszó mezők

#### Validációs üzenetek:
- Posta email nem található: Részletes hibaüzenet
- Inaktív fiók figyelmeztetés: Sárga banner

## Admin Jóváhagyási Rendszer

### UserController bővítése:

#### Új metódusok:
```php
public function activate(User $user)    // Felhasználó aktiválása
public function deactivate(User $user)  // Felhasználó inaktiválása
```

#### Route-ok:
```php
PATCH /admin/users/{user}/activate
PATCH /admin/users/{user}/deactivate
```

#### Index metódus:
```php
'users' => User::with(['roles', 'companies', 'contact', 'orgUnit', 'garage'])->get()
```

## User Model Bővítése

### Új kapcsolatok:
```php
public function contact()  // belongsTo Contact
public function orgUnit()  // belongsTo OrgUnit  
public function garage()   // belongsTo Garage
```

### Új scope-ok:
```php
public function scopeActive($query)   // Csak aktív felhasználók
public function scopePosta($query)    // Posta felhasználók
public function scopePartner($query)  // Partner felhasználók
```

## Biztonsági Megfontolások

### Email Validáció:
- **Posta domain**: Szigorú @posta.hu ellenőrzés
- **Contact létezés**: Kötelező ellenőrzés a contacts táblában
- **Egyediség**: Email cím egyedi kell legyen

### Admin Jóváhagyás:
- **Alapértelmezett inaktív**: Minden új user inaktív
- **Login blokkolás**: Inaktív userek nem jelentkezhetnek be
- **Admin kontroll**: Csak admin aktiválhat felhasználókat

### Adatvédelem:
- **Minimális adatok**: Csak szükséges mezők tárolása
- **Kapcsolatok**: Soft delete támogatás foreign key-ekkel

## Jövőbeli Fejlesztések

### Admin Felület:
- [ ] Pending regisztrációk lista
- [ ] Bulk aktiválás/inaktiválás
- [ ] Email értesítések aktiváláskor
- [ ] Regisztrációs statisztikák

### Felhasználói Élmény:
- [ ] Email értesítés regisztráció után
- [ ] Státusz ellenőrzés oldal
- [ ] Profil szerkesztés regisztrált adatokkal

### Integráció:
- [ ] LDAP integráció Posta felhasználókhoz
- [ ] Automatikus szerepkör hozzárendelés
- [ ] Szervezeti hierarchia alapú jogosultságok

## Tesztelési Útmutató

### Posta Regisztráció:
1. Válaszd "Magyar Posta Zrt."
2. Adj meg @posta.hu email címet (contacts táblában létező)
3. Ellenőrizd az automatikus kitöltést
4. Töltsd ki a jelszót
5. Regisztrálj → átirányítás login-ra üzenettel

### Partner Regisztráció:
1. Válaszd "Más partner"
2. Töltsd ki az összes mezőt
3. Munkakör: válassz meglévőt vagy írj újat
4. Regisztrálj → átirányítás login-ra üzenettel

### Admin Jóváhagyás:
1. Admin bejelentkezés
2. Users lista → új regisztrált user (inaktív)
3. Activate gomb → user aktív lesz
4. User most már be tud jelentkezni

A rendszer készen áll a használatra! 🚀
