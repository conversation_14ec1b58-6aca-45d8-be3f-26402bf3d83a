import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { useForm } from '@inertiajs/react';
import { Building2, LocateFixed, Mail, Map, MapPin, Phone, Plus, X } from 'lucide-react';
import { FormEventHandler, useEffect, useState } from 'react';

interface ServiceProviderFormProps {
    serviceProvider?: {
        id?: number;
        name: string;
        postal_code: string;
        street: string;
        city: string;
        phone?: string;
        emails?: string[];
        is_active: boolean;
    } | null;
    onCancel: () => void;
}

export default function ServiceProviderForm({ serviceProvider, onCancel }: ServiceProviderFormProps) {
    const [emails, setEmails] = useState<string[]>(serviceProvider?.emails || ['']);

    const { data, setData, post, put, processing, errors, wasSuccessful } = useForm({
        name: serviceProvider?.name || '',
        postal_code: serviceProvider?.postal_code || '',
        street: serviceProvider?.street || '',
        city: serviceProvider?.city || '',
        phone: serviceProvider?.phone || '',
        emails: serviceProvider?.emails || [''],
        is_active: serviceProvider?.is_active ?? true,
    });

    useEffect(() => {
        if (wasSuccessful) {
            onCancel();
        }
    }, [wasSuccessful, onCancel]);

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();

        // Üres email címek kiszűrése
        const filteredEmails = emails.filter((email) => email.trim() !== '');
        setData('emails', filteredEmails);

        if (serviceProvider?.id) {
            put(route('admin.service-providers.update', serviceProvider.id));
        } else {
            post(route('admin.service-providers.store'));
        }
    };

    const addEmail = () => {
        const newEmails = [...emails, ''];
        setEmails(newEmails);
        setData('emails', newEmails);
    };

    const removeEmail = (index: number) => {
        const newEmails = emails.filter((_, i) => i !== index);
        setEmails(newEmails);
        setData('emails', newEmails);
    };

    const updateEmail = (index: number, value: string) => {
        const newEmails = [...emails];
        newEmails[index] = value;
        setEmails(newEmails);
        setData('emails', newEmails);
    };

    return (
        <form onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
            <div className="flex-1 space-y-6 overflow-y-auto p-6">
                {/* Alapadatok */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium text-gray-900">Alapadatok</h3>

                    <div className="space-y-2">
                        <Label htmlFor="name">Szerviz neve *</Label>
                        <div className="relative">
                            <Building2 className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="name"
                                type="text"
                                value={data.name}
                                onChange={(e) => setData('name', e.target.value)}
                                disabled={processing}
                                placeholder="pl. AutoSzerviz Kft."
                                className={cn('pl-10', errors.name && 'border-red-500')}
                            />
                        </div>
                        {errors.name && <div className="text-sm text-red-500">{errors.name}</div>}
                    </div>
                </div>

                {/* Cím adatok */}
                <div className="space-y-4">
                    <h3 className="text-md flex items-center gap-2 font-medium text-gray-900">
                        <MapPin className="h-4 w-4" />
                        Cím adatok
                    </h3>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                        <div className="space-y-2">
                            <Label htmlFor="postal_code">Irányítószám *</Label>
                            <div className="relative">
                                <LocateFixed className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="postal_code"
                                    type="text"
                                    value={data.postal_code}
                                    onChange={(e) => setData('postal_code', e.target.value)}
                                    disabled={processing}
                                    placeholder="1234"
                                    className={cn('pl-10', errors.postal_code && 'border-red-500')}
                                />
                            </div>
                            {errors.postal_code && <div className="text-sm text-red-500">{errors.postal_code}</div>}
                        </div>

                        <div className="space-y-2 md:col-span-2">
                            <Label htmlFor="city">Város *</Label>
                            <div className="relative">
                                <Map className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="city"
                                    type="text"
                                    value={data.city}
                                    onChange={(e) => setData('city', e.target.value)}
                                    disabled={processing}
                                    placeholder="Budapest"
                                    className={cn('pl-10', errors.city && 'border-red-500')}
                                />
                            </div>
                            {errors.city && <div className="text-sm text-red-500">{errors.city}</div>}
                        </div>
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="street">Utca, házszám *</Label>
                        <div className="relative">
                            <MapPin className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="street"
                                type="text"
                                value={data.street}
                                onChange={(e) => setData('street', e.target.value)}
                                disabled={processing}
                                placeholder="Fő utca 123."
                                className={cn('pl-10', errors.street && 'border-red-500')}
                            />
                        </div>
                        {errors.street && <div className="text-sm text-red-500">{errors.street}</div>}
                    </div>
                </div>

                {/* Elérhetőségek */}
                <div className="space-y-4">
                    <h3 className="text-md font-medium text-gray-900">Elérhetőségek</h3>

                    <div className="space-y-2">
                        <Label htmlFor="phone">Telefonszám</Label>
                        <div className="relative">
                            <Phone className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="phone"
                                type="tel"
                                value={data.phone}
                                onChange={(e) => setData('phone', e.target.value)}
                                disabled={processing}
                                placeholder="+36 1 234 5678"
                                className={cn('pl-10', errors.phone && 'border-red-500')}
                            />
                        </div>
                        {errors.phone && <div className="text-sm text-red-500">{errors.phone}</div>}
                    </div>

                    <div className="space-y-2">
                        <Label>Email címek</Label>
                        <div className="space-y-2">
                            {emails.map((email, index) => (
                                <div key={index} className="flex items-center gap-2">
                                    <div className="relative w-full">
                                        <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                        <Input
                                            type="email"
                                            value={email}
                                            onChange={(e) => updateEmail(index, e.target.value)}
                                            disabled={processing}
                                            placeholder="<EMAIL>"
                                            className={cn('pl-10', (errors as any)[`emails.${index}`] && 'border-red-500')}
                                        />
                                    </div>
                                    {emails.length > 1 && (
                                        <Button type="button" variant="outline" size="sm" onClick={() => removeEmail(index)} disabled={processing}>
                                            <X className="h-4 w-4" />
                                        </Button>
                                    )}
                                </div>
                            ))}
                            <Button type="button" variant="outline" size="sm" onClick={addEmail} disabled={processing} className="w-full">
                                <Plus className="mr-2 h-4 w-4" />
                                Email cím hozzáadása
                            </Button>
                        </div>
                        {errors.emails && <div className="text-sm text-red-500">{errors.emails}</div>}
                    </div>
                </div>

                {/* Állapot */}
                <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                        <input
                            type="checkbox"
                            id="is_active"
                            checked={data.is_active}
                            onChange={(e) => setData('is_active', e.target.checked)}
                            disabled={processing}
                            className="rounded border-gray-300"
                        />
                        <Label htmlFor="is_active" className="cursor-pointer">
                            Aktív szerviz
                        </Label>
                    </div>
                    {!data.is_active && (
                        <div className="rounded-lg border border-amber-200 bg-amber-50 p-3">
                            <p className="text-sm text-amber-700">⚠️ Inaktív szervizek nem jelennek meg a javítási igény feladásakor.</p>
                        </div>
                    )}
                </div>

                {/* Gombok */}
            </div>

            <DialogFooter className="flex-shrink-0 p-6">
                <Button type="button" variant="outline" onClick={onCancel} disabled={processing}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {processing ? 'Mentés...' : serviceProvider?.id ? 'Mentés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </form>
    );
}
