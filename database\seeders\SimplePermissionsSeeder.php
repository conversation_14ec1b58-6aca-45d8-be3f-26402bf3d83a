<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class SimplePermissionsSeeder extends Seeder
{
    public function run(): void
    {
        $permissions = [
            'access_system_admin_features' => [
                'display_name' => 'Rendszer admin funkciók',
                'description' => 'Hozzáférés a rendszer adminisztrációs funkciókhoz',
                'category' => 'system',
                'is_dangerous' => true,
                'sort_order' => 100
            ],
            'manage_users' => [
                'display_name' => 'Partner felhasználók kezelése',
                'description' => 'Partner felhasználók jóváhagyása és kezelése',
                'category' => 'user_management',
                'is_dangerous' => false,
                'sort_order' => 200
            ],
            'manage_posta_users' => [
                'display_name' => 'Posta felhasználók kezelése',
                'description' => 'Magyar Posta felhasználók jóváhagyása és kezelése',
                'category' => 'user_management',
                'is_dangerous' => false,
                'sort_order' => 201
            ],
            'manage_roles' => [
                'display_name' => 'Szerepkörök kezelése',
                'description' => 'Szerepkörök létrehozása, módosítása és törlése',
                'category' => 'user_management',
                'is_dangerous' => true,
                'sort_order' => 202
            ],
            'submit_repair_request' => [
                'display_name' => 'Javítási kérelem beküldése',
                'description' => 'Új javítási kérelmek beküldése',
                'category' => 'repair_management',
                'is_dangerous' => false,
                'sort_order' => 300
            ],
            'view_repair_status' => [
                'display_name' => 'Javítási státusz megtekintése',
                'description' => 'Javítási kérelmek státuszának megtekintése',
                'category' => 'repair_management',
                'is_dangerous' => false,
                'sort_order' => 302
            ],
            'upload_documents' => [
                'display_name' => 'Dokumentumok feltöltése',
                'description' => 'Dokumentumok és fájlok feltöltése',
                'category' => 'data_management',
                'is_dangerous' => false,
                'sort_order' => 400
            ],
            'view_logs' => [
                'display_name' => 'Naplók megtekintése',
                'description' => 'Rendszer és felhasználói naplók megtekintése',
                'category' => 'reporting',
                'is_dangerous' => false,
                'sort_order' => 500
            ]
        ];

        foreach ($permissions as $name => $data) {
            Permission::updateOrCreate(
                ['name' => $name],
                $data
            );
        }

        $this->command->info('Enhanced permissions updated successfully.');
    }
}
