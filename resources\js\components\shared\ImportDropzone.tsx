import { Button } from '@/components/ui/button';
import { router } from '@inertiajs/react';
import { FileText, Loader2, Upload } from 'lucide-react';
import { DragEvent, useRef, useState } from 'react';

interface ImportDropzoneProps {
    uploadRouteName: string;
    onDownloadSample: () => void;
    onUploadSuccess: () => void;
    supportedFormats?: string;
    title?: string;
    description?: string;
}

export function ImportDropzone({
    uploadRouteName,
    onDownloadSample,
    onUploadSuccess,
    supportedFormats = '.xlsx, .xls, .csv',
    title = 'Húzza ide a fájlt, vagy kattintson a feltöltéshez',
    description = `Támogatott formátumok: ${supportedFormats}`,
}: ImportDropzoneProps) {
    const [isImporting, setIsImporting] = useState(false);
    const [isDragging, setIsDragging] = useState(false);
    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleFileUpload = (file: File | null) => {
        if (!file || isImporting) return;

        const formData = new FormData();
        formData.append('file', file);

        router.post(route(uploadRouteName), formData, {
            forceFormData: true,
            onStart: () => setIsImporting(true),
            onFinish: () => {
                setIsImporting(false);
                if (fileInputRef.current) {
                    fileInputRef.current.value = '';
                }
            },
            onSuccess: () => onUploadSuccess(),
        });
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        handleFileUpload(event.target.files?.[0] || null);
    };

    const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        if (!isImporting) setIsDragging(true);
    };

    const handleDragLeave = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
    };

    const handleDrop = (e: DragEvent<HTMLDivElement>) => {
        e.preventDefault();
        e.stopPropagation();
        setIsDragging(false);
        if (isImporting) return;
        const files = e.dataTransfer.files;
        if (files && files.length > 0) handleFileUpload(files[0]);
    };

    return (
        <>
            <div
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
                onDrop={handleDrop}
                onClick={() => !isImporting && fileInputRef.current?.click()}
                className={`flex cursor-pointer flex-col items-center justify-center space-y-4 rounded-md border-2 border-dashed p-8 text-center transition-colors ${isDragging ? 'border-primary bg-primary/10' : 'border-gray-300 hover:border-primary/50'} ${isImporting && 'cursor-not-allowed opacity-50'}`}
            >
                {isImporting ? (
                    <>
                        <Loader2 className="h-10 w-10 animate-spin text-primary" />
                        <p className="font-medium">Feltöltés folyamatban...</p>
                    </>
                ) : (
                    <>
                        <Upload className="h-10 w-10 text-gray-400" />
                        <p className="font-medium">{title}</p>
                        <p className="text-sm text-muted-foreground">{description}</p>
                        <div className="pt-4">
                            <Button
                                variant="secondary"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    onDownloadSample();
                                }}
                                disabled={isImporting}
                            >
                                <FileText className="mr-2 h-4 w-4" />
                                Minta fájl letöltése
                            </Button>
                        </div>
                    </>
                )}
            </div>
            <input ref={fileInputRef} type="file" accept={supportedFormats} onChange={handleFileChange} className="hidden" />
        </>
    );
}
