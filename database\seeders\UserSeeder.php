<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 1. SystemAdmin szerepkör létrehozása (ha nincs)
        $role = Role::firstOrCreate(['name' => 'SystemAdmin']);

        // 2. Admin felhasználó létrehozása
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => '<PERSON><PERSON><PERSON> (Admin)',
                'password' => Hash::make('Pr153750'), // Használj biztonságosabb jelszót élesben!
                'email_verified_at' => now(),
            ]
        );

        // 3. Szerepkör hozzárendelése
        if (! $user->hasRole('SystemAdmin')) {
            $user->assignRole($role);
        }
    }
}
