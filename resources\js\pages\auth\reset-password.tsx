import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle, Mail } from 'lucide-react';
import { FormEventHandler, useState } from 'react';

import PasswordInput from '@/components/form/password-input';
import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import useTranslations from '@/hooks/use-translations';
import AuthLayout from '@/layouts/auth-layout';

interface ResetPasswordProps {
    token: string;
    email: string;
}

type ResetPasswordForm = {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
};

export default function ResetPassword({ token, email }: ResetPasswordProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<ResetPasswordForm>>({
        token: token,
        email: email,
        password: '',
        password_confirmation: '',
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('password.store'), {
            onFinish: () => reset('password', 'password_confirmation'),
        });
    };

    const { t } = useTranslations('auth');

    const [showPassword, setShowPassword] = useState(false);
    const [showPasswordConfirmation, setShowPasswordConfirmation] = useState(false);

    return (
        <AuthLayout title={t('reset_password')} description={t('reset_password_description')}>
            <Head title={t('reset_password')} />

            <form onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">{t('email')}</Label>
                        <div className="relative">
                            <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input
                                id="email"
                                type="email"
                                name="email"
                                autoComplete="email"
                                tabIndex={1}
                                value={data.email}
                                className="mt-1 block w-full pl-10"
                                readOnly
                                onChange={(e) => setData('email', e.target.value)}
                            />
                        </div>
                        <InputError message={errors.email} className="mt-2" />
                    </div>

                    <PasswordInput
                        id="password"
                        name="password"
                        label={t('new_password')}
                        value={data.password}
                        tabIndex={2}
                        onChange={(e) => setData('password', e.target.value)}
                        error={errors.password}
                        placeholder={t('new_password')}
                        autoFocus
                    />

                    <PasswordInput
                        id="password_confirmation"
                        name="password_confirmation"
                        label={t('confirm_password')}
                        value={data.password_confirmation}
                        tabIndex={3}
                        onChange={(e) => setData('password_confirmation', e.target.value)}
                        error={errors.password_confirmation}
                        placeholder={t('confirm_password')}
                    />

                    <Button type="submit" className="mt-4 w-full" disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        {t('reset_password_button')}
                    </Button>
                </div>
            </form>
        </AuthLayout>
    );
}
