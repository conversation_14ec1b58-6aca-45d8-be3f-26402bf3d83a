<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UserRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->ignore($this->user),
            ],
            'password' => [$this->isMethod('post') ? 'required' : 'nullable', 'min:6', 'confirmed'],
            'password_confirmation' => [$this->isMethod('post') ? 'required' : 'nullable', 'min:6'],
            'is_active' => ['boolean'],
            'roles' => ['array'],
            'roles.*' => ['exists:roles,id'],
            'companies' => ['array'],
            'companies.*' => ['exists:companies,id'],
        ];
    }
}