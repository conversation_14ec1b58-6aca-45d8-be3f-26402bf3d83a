<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use App\Http\Controllers\Admin\PermissionController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\CompanyController;
use App\Http\Controllers\Admin\ContactController;
use App\Http\Controllers\Admin\OrgUnitController;
use App\Http\Controllers\Admin\GarageController;
use App\Http\Controllers\Admin\UserApprovalController;
use App\Http\Controllers\Admin\ServiceProviderController;
use App\Http\Controllers\Admin\ServiceProviderWebhookController;
use App\Http\Controllers\Admin\PostalVehicleController;
use App\Http\Controllers\Api\ContactLookupController;
use App\Http\Controllers\Api\VehicleDataController;

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('/', function () {
        return Inertia::render('dashboard');
    })->name('home');
    
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');
});

Route::middleware(['auth', 'permission:access_system_admin_features'])->group(function () {
    Route::resource('admin/roles', RoleController::class);
    Route::resource('admin/permissions', PermissionController::class);
    Route::resource('admin/users', UserController::class);
    Route::patch('admin/users/{user}/activate', [UserController::class, 'activate'])->name('admin.users.activate');
    Route::patch('admin/users/{user}/deactivate', [UserController::class, 'deactivate'])->name('admin.users.deactivate');

    // Szervizek kezelése
    Route::resource('admin/service-providers', ServiceProviderController::class)->names([
        'index' => 'admin.service-providers.index',
        'create' => 'admin.service-providers.create',
        'store' => 'admin.service-providers.store',
        'show' => 'admin.service-providers.show',
        'edit' => 'admin.service-providers.edit',
        'update' => 'admin.service-providers.update',
        'destroy' => 'admin.service-providers.destroy',
    ]);
    Route::patch('admin/service-providers/{serviceProvider}/activate', [ServiceProviderController::class, 'activate'])->name('admin.service-providers.activate');
    Route::get('admin/service-providers/export', [ServiceProviderController::class, 'export'])->name('admin.service-providers.export');
    Route::post('admin/service-providers/import', [ServiceProviderController::class, 'import'])->name('admin.service-providers.import');

    // Szerviz webhookok kezelése
    Route::resource('admin/service-providers/{serviceProvider}/webhooks', ServiceProviderWebhookController::class)->names([
        'index' => 'admin.service-providers.webhooks.index',
        'create' => 'admin.service-providers.webhooks.create',
        'store' => 'admin.service-providers.webhooks.store',
        'edit' => 'admin.service-providers.webhooks.edit',
        'update' => 'admin.service-providers.webhooks.update',
        'destroy' => 'admin.service-providers.webhooks.destroy',
    ]);
    Route::post('admin/service-providers/{serviceProvider}/webhooks/{webhook}/test', [ServiceProviderWebhookController::class, 'test'])->name('admin.service-providers.webhooks.test');
    Route::patch('admin/service-providers/{serviceProvider}/webhooks/{webhook}/toggle', [ServiceProviderWebhookController::class, 'toggle'])->name('admin.service-providers.webhooks.toggle');

    // User Approval routes - require manage_users OR manage_posta_users permission
    Route::middleware(['permission:manage_users|manage_posta_users'])->group(function () {
        Route::get('admin/user-approval', [UserApprovalController::class, 'index'])->name('admin.user-approval.index');
        Route::get('admin/user-approval/{user}', [UserApprovalController::class, 'show'])->name('admin.user-approval.show');
        Route::post('admin/user-approval/{user}/approve', [UserApprovalController::class, 'approve'])->name('admin.user-approval.approve');
        Route::post('admin/user-approval/{user}/reject', [UserApprovalController::class, 'reject'])->name('admin.user-approval.reject');
    });
    Route::resource('admin/companies', CompanyController::class);

    // Contact routes
    Route::get('admin/contacts', [ContactController::class, 'index'])->name('admin.contacts.index');
    Route::post('admin/contacts', [ContactController::class, 'store'])->name('admin.contacts.store');
    Route::put('admin/contacts/{contact}', [ContactController::class, 'update'])->name('admin.contacts.update');
    Route::delete('admin/contacts/{contact}', [ContactController::class, 'destroy'])->name('admin.contacts.destroy');
    Route::get('admin/contacts/export', [ContactController::class, 'export'])->name('admin.contacts.export');
    Route::post('admin/contacts/import', [ContactController::class, 'import'])->name('admin.contacts.import');
    Route::get('admin/contacts/download-sample', [ContactController::class, 'downloadSample'])->name('admin.contacts.download-sample');

    // Postai járművek kezelése - manage_postal_vehicles jogosultság szükséges
    Route::middleware(['permission:manage_postal_vehicles'])->group(function () {
        Route::resource('admin/postal-vehicles', PostalVehicleController::class)->names([
            'index' => 'admin.postal-vehicles.index',
            'store' => 'admin.postal-vehicles.store',
            'update' => 'admin.postal-vehicles.update',
            'destroy' => 'admin.postal-vehicles.destroy',
        ]);
        Route::get('admin/postal-vehicles/export', [PostalVehicleController::class, 'export'])->name('admin.postal-vehicles.export');

        // Import külön jogosultságot igényel
        Route::middleware(['permission:import_postal_vehicles'])->group(function () {
            Route::post('admin/postal-vehicles/import', [PostalVehicleController::class, 'import'])->name('admin.postal-vehicles.import');
        });
    });

    // Org Units and Garages for dynamic creation
    Route::post('admin/org-units', [OrgUnitController::class, 'store'])->name('admin.org-units.store');
    Route::post('admin/garages', [GarageController::class, 'store'])->name('admin.garages.store');
});

// API routes for registration
Route::get('/api/contacts/lookup', [ContactLookupController::class, 'lookup'])->name('api.contacts.lookup');
Route::get('/api/service-providers', [ServiceProviderController::class, 'apiIndex'])->name('api.service-providers.index');

// Vehicle data API routes (NHTSA integration)
Route::middleware(['auth'])->group(function () {
    Route::get('/api/vehicle-data/manufacturers', [VehicleDataController::class, 'getManufacturers'])->name('api.vehicle-data.manufacturers');
    Route::get('/api/vehicle-data/models', [VehicleDataController::class, 'getModels'])->name('api.vehicle-data.models');
    Route::post('/api/vehicle-data/clear-cache', [VehicleDataController::class, 'clearCache'])->name('api.vehicle-data.clear-cache')->middleware('permission:access_system_admin_features');
});



require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
