<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Regisztrációs mezők hozzáadása
            if (!Schema::hasColumn('users', 'company_type')) {
                $table->enum('company_type', ['posta', 'partner'])->nullable()->after('is_active');
            }

            if (!Schema::hasColumn('users', 'company_name')) {
                $table->string('company_name')->nullable()->after('company_type');
            }

            if (!Schema::hasColumn('users', 'tax_number')) {
                $table->string('tax_number')->nullable()->after('company_name');
            }

            if (!Schema::hasColumn('users', 'position')) {
                $table->string('position')->nullable()->after('tax_number');
            }

            if (!Schema::hasColumn('users', 'phone')) {
                $table->string('phone')->nullable()->after('position');
            }

            // Kapcsolatok (Posta felhasználókhoz)
            if (!Schema::hasColumn('users', 'contact_id')) {
                $table->unsignedBigInteger('contact_id')->nullable()->after('phone');
                $table->foreign('contact_id')->references('id')->on('contacts')->onDelete('set null');
            }

            if (!Schema::hasColumn('users', 'org_unit_id')) {
                $table->unsignedBigInteger('org_unit_id')->nullable()->after('contact_id');
                $table->foreign('org_unit_id')->references('id')->on('org_units')->onDelete('set null');
            }

            if (!Schema::hasColumn('users', 'garage_id')) {
                $table->unsignedBigInteger('garage_id')->nullable()->after('org_unit_id');
                $table->foreign('garage_id')->references('id')->on('garages')->onDelete('set null');
            }

            // Admin jóváhagyás mezők
            if (!Schema::hasColumn('users', 'approved_at')) {
                $table->timestamp('approved_at')->nullable()->after('garage_id');
            }

            if (!Schema::hasColumn('users', 'approved_by')) {
                $table->unsignedBigInteger('approved_by')->nullable()->after('approved_at');
                $table->foreign('approved_by')->references('id')->on('users')->onDelete('set null');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Admin jóváhagyás mezők törlése
            if (Schema::hasColumn('users', 'approved_by')) {
                $table->dropForeign(['approved_by']);
                $table->dropColumn('approved_by');
            }

            if (Schema::hasColumn('users', 'approved_at')) {
                $table->dropColumn('approved_at');
            }

            // Kapcsolatok törlése
            if (Schema::hasColumn('users', 'garage_id')) {
                $table->dropForeign(['garage_id']);
                $table->dropColumn('garage_id');
            }

            if (Schema::hasColumn('users', 'org_unit_id')) {
                $table->dropForeign(['org_unit_id']);
                $table->dropColumn('org_unit_id');
            }

            if (Schema::hasColumn('users', 'contact_id')) {
                $table->dropForeign(['contact_id']);
                $table->dropColumn('contact_id');
            }

            // Regisztrációs mezők törlése
            if (Schema::hasColumn('users', 'phone')) {
                $table->dropColumn('phone');
            }

            if (Schema::hasColumn('users', 'position')) {
                $table->dropColumn('position');
            }

            if (Schema::hasColumn('users', 'tax_number')) {
                $table->dropColumn('tax_number');
            }

            if (Schema::hasColumn('users', 'company_name')) {
                $table->dropColumn('company_name');
            }

            if (Schema::hasColumn('users', 'company_type')) {
                $table->dropColumn('company_type');
            }
        });
    }
};
