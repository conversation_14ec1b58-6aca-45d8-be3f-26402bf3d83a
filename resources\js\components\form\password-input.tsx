// components/form/password-input.tsx

import InputError from '@/components/input-error';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock } from 'lucide-react';
import { ReactNode, useState } from 'react';

interface PasswordInputProps {
    id: string;
    name: string;
    label: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    error?: string;
    placeholder?: string;
    autoFocus?: boolean;
    autoComplete?: string;
    required?: boolean;
    disabled?: boolean;
    tabIndex?: number;
    actionSlot?: ReactNode; // például a "Forgot password?" link
}

export default function PasswordInput({
    id,
    name,
    label,
    value,
    onChange,
    error,
    placeholder,
    autoFocus = false,
    autoComplete = 'new-password',
    required = false,
    disabled = false,
    tabIndex,
    actionSlot,
}: PasswordInputProps) {
    const [showPassword, setShowPassword] = useState(false);

    return (
        <div className="grid gap-2">
            <div className="flex items-center">
                <Label htmlFor={id}>{label}</Label>
                {actionSlot && <div className="ml-auto text-sm">{actionSlot}</div>}
            </div>
            <div className="relative">
                <Lock className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                    id={id}
                    name={name}
                    type={showPassword ? 'text' : 'password'}
                    autoComplete={autoComplete}
                    value={value}
                    onChange={onChange}
                    placeholder={placeholder}
                    autoFocus={autoFocus}
                    required={required}
                    disabled={disabled}
                    tabIndex={tabIndex}
                    className="pl-10"
                />
                <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute top-3 right-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                >
                    {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
            </div>
            {error && <InputError message={error} />}
        </div>
    );
}
