<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Redirect;


class PermissionController extends Controller
{
    public function index()
    {
        return Inertia::render('admin/permissions/index', [
            'permissions' => Permission::with('roles:id,name')
                ->select('id', 'name', 'display_name', 'description', 'category', 'is_dangerous', 'sort_order')
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get()
        ]);
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'display_name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:255',
            'is_dangerous' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        Permission::create($validated);

        return Redirect::back()->with('success', 'Jogosultság létrehozva.');
    }

    public function update(Request $request, Permission $permission)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name,' . $permission->id,
            'display_name' => 'nullable|string|max:255',
            'description' => 'nullable|string|max:1000',
            'category' => 'required|string|max:255',
            'is_dangerous' => 'boolean',
            'sort_order' => 'integer|min:0',
        ]);

        $permission->update($validated);

        return Redirect::back()->with('success', 'Jogosultság frissítve.');
    }

    public function destroy(Permission $permission)
    {
        $permission->delete();

        return Redirect::back()->with('success', 'Jogosultság törölve.');
    }
}
