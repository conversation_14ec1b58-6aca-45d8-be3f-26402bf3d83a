# Felhasználó Jóváhagyási Rendszer

## Áttekintés

A felhasználó jóváhagyási rendszer lehetővé teszi az adminisztrátorok számára, hogy ellenőrizzék és jóváhagyják az új regisztrációkat. A rendszer jogosultság alapú hozzáférést biztosít:

- **`manage_users`** - Partner felhasználók jóváhagyása
- **`manage_posta_users`** - Magyar Posta felhasználók jóváhagyása

## Jogosultságok

### Új Permissions
```php
'manage_users' => 'Partner felhasználók jóváhagyása'
'manage_posta_users' => 'Posta felhasználók jóváhagyása'
```

### Szerepkör Hozzárendelések
- **SystemAdmin**: Mindkét jogosults<PERSON>g (`manage_users` + `manage_posta_users`)
- **PostaAdmin**: Csak Posta jogosultság (`manage_posta_users`)

## Funkciók

### 1. 📋 Jóváhagyási Lista
- **URL**: `/admin/user-approval`
- **Jogosultság**: `manage_users|manage_posta_users`
- **Funkciók**:
  - Inaktív felhasználók listája
  - Jogosultság alapú szűrés
  - DataTable komponens
  - Regisztráció dátuma
  - Cég típus megjelenítése

### 2. 👁️ Részletek Modal
- **Komponens**: `UserApprovalModal`
- **Funkciók**:
  - Teljes felhasználói adatok
  - Posta/Partner specifikus mezők
  - Jóváhagyás/Elutasítás gombok
  - Indoklás mező (elutasításnál kötelező)

### 3. ✅ Jóváhagyás Folyamat
- **Endpoint**: `POST /admin/user-approval/{user}/approve`
- **Művelet**:
  - `is_active = true`
  - `approved_at = now()`
  - `approved_by = current_user_id`
  - Email értesítés küldése

### 4. ❌ Elutasítás Folyamat
- **Endpoint**: `POST /admin/user-approval/{user}/reject`
- **Validáció**: Indoklás kötelező (min. 10 karakter)
- **Művelet**:
  - Email értesítés indoklással
  - Felhasználó törlése

## Backend Implementáció

### UserApprovalController

#### index() - Lista megjelenítés
```php
// Jogosultság alapú szűrés
if ($user->can('manage_posta_users') && !$user->can('manage_users')) {
    $query->where('company_type', 'posta');
} elseif ($user->can('manage_users') && !$user->can('manage_posta_users')) {
    $query->where('company_type', 'partner');
}
```

#### approve() - Jóváhagyás
```php
$user->update([
    'is_active' => true,
    'approved_at' => now(),
    'approved_by' => $currentUser->id,
]);

$user->notify(new UserApprovalNotification(true, null));
```

#### reject() - Elutasítás
```php
$user->notify(new UserApprovalNotification(false, $request->reason));
$user->delete();
```

### Email Értesítések

#### UserApprovalNotification
- **Jóváhagyás**: Üdvözlő email bejelentkezési linkkel
- **Elutasítás**: Indoklással ellátott email

```php
// Jóváhagyott
->subject('Regisztráció jóváhagyva - RepairFlow')
->line('Fiókja mostantól aktív, bejelentkezhet a rendszerbe.')
->action('Bejelentkezés', url('/login'))

// Elutasított
->subject('Regisztráció elutasítva - RepairFlow')
->line('**Elutasítás indoka:**')
->line($this->rejectionReason)
```

## Frontend Implementáció

### Lista Oldal (`admin/user-approval/index.tsx`)

#### Jogosultság Alapú Megjelenítés
```tsx
const canManage = (user.company_type === 'posta' && canManagePostaUsers) || 
                 (user.company_type === 'partner' && canManageUsers);
```

#### DataTable Oszlopok
- **Név** - Felhasználó neve
- **Email** - Email cím
- **Típus** - Badge (Magyar Posta / Partner)
- **Cég/Szervezet** - Szervezeti egység vagy cégnév
- **Regisztráció** - Dátum formázva
- **Műveletek** - Részletek gomb (jogosultság alapján)

### Modal Komponens (`UserApprovalModal.tsx`)

#### Adatok Megjelenítése
```tsx
// Posta specifikus
{user.company_type === 'posta' && (
    <div className="bg-blue-50 border border-blue-200">
        <h3>Magyar Posta adatok</h3>
        <p>Felhasználónév: {user.contact.username}</p>
        <p>Szervezeti egység: {user.org_unit.name}</p>
        <p>Garázs: {user.garage.name}</p>
    </div>
)}

// Partner specifikus
{user.company_type === 'partner' && (
    <div className="bg-green-50 border border-green-200">
        <h3>Partner cég adatok</h3>
        <p>Cégnév: {user.company_name}</p>
        <p>Adószám: {user.tax_number}</p>
        <p>Munkakör: {user.position}</p>
        <p>Telefon: {user.phone}</p>
    </div>
)}
```

#### Műveletek
- **Jóváhagyás**: Megerősítő dialog
- **Elutasítás**: Indoklás textarea (kötelező, min. 10 karakter)

## Navigáció

### Admin Menü
```tsx
{
    title: 'Felhasználó jóváhagyás',
    href: '/admin/user-approval',
    icon: UserCheck,
}
```

### Route Védelem
```php
Route::middleware(['permission:manage_users|manage_posta_users'])->group(function () {
    Route::get('admin/user-approval', [UserApprovalController::class, 'index']);
    // ...
});
```

## Adatbázis Módosítások

### Users Tábla Bővítése
```sql
approved_at TIMESTAMP NULL     -- Jóváhagyás időpontja
approved_by BIGINT UNSIGNED NULL  -- Jóváhagyó admin ID
```

### Kapcsolatok
```php
// User model
public function approvedBy()
{
    return $this->belongsTo(User::class, 'approved_by');
}
```

## Használati Útmutató

### Admin Felhasználó Számára

#### 1. Jóváhagyási Lista Elérése
1. Bejelentkezés admin fiókkal
2. Navigáció: **Admin** → **Felhasználó jóváhagyás**
3. Lista megjelenítése jogosultság alapján

#### 2. Felhasználó Jóváhagyása
1. **Részletek** gomb → Modal megnyitása
2. Adatok áttekintése
3. **Jóváhagyás** gomb → Megerősítés
4. Felhasználó aktiválva + email küldve

#### 3. Felhasználó Elutasítása
1. **Részletek** gomb → Modal megnyitása
2. **Elutasítás** gomb → Dialog megnyitása
3. **Indoklás megadása** (kötelező, min. 10 karakter)
4. **Elutasítás** → Email küldve + felhasználó törölve

### Jogosultság Alapú Hozzáférés

#### SystemAdmin
- ✅ Látja az összes jóváhagyásra váró felhasználót
- ✅ Jóváhagyhatja Posta felhasználókat
- ✅ Jóváhagyhatja Partner felhasználókat

#### PostaAdmin
- ✅ Csak Posta felhasználókat látja
- ✅ Csak Posta felhasználókat hagyhatja jóvá
- ❌ Partner felhasználók nem láthatók

#### Egyéb Szerepkörök
- ❌ Nincs hozzáférés a jóváhagyási felülethez

## Email Template-ek

### Jóváhagyott Regisztráció
```
Tárgy: Regisztráció jóváhagyva - RepairFlow

Kedves [Név]!

Örömmel értesítjük, hogy a RepairFlow rendszerben történt regisztrációját jóváhagytuk.
Fiókja mostantól aktív, bejelentkezhet a rendszerbe.

[Bejelentkezés gomb]

Köszönjük, hogy csatlakozott hozzánk!

Üdvözlettel,
RepairFlow Csapat
```

### Elutasított Regisztráció
```
Tárgy: Regisztráció elutasítva - RepairFlow

Kedves [Név]!

Sajnálattal értesítjük, hogy a RepairFlow rendszerben történt regisztrációját nem tudtuk jóváhagyni.

Elutasítás indoka:
[Admin által megadott indoklás]

Ha kérdése van, vagy úgy érzi, hogy tévedés történt, kérjük, vegye fel velünk a kapcsolatot.

Köszönjük megértését.

Üdvözlettel,
RepairFlow Csapat
```

## Biztonsági Megfontolások

### Jogosultság Ellenőrzés
- **Route szinten**: Middleware védelem
- **Controller szinten**: Explicit jogosultság ellenőrzés
- **Frontend szinten**: UI elemek elrejtése

### Audit Trail
- **approved_at**: Jóváhagyás időpontja
- **approved_by**: Jóváhagyó admin azonosítója
- **Email log**: Értesítések naplózása

### Adatvédelem
- **Elutasított felhasználók törlése**: GDPR megfelelőség
- **Indoklás tárolása**: Csak email-ben, adatbázisban nem

## Jövőbeli Fejlesztések

### Funkcionális
- [ ] Bulk jóváhagyás/elutasítás
- [ ] Jóváhagyási határidő beállítása
- [ ] Automatikus emlékeztető emailek
- [ ] Jóváhagyási statisztikák

### Technikai
- [ ] Queue-ba tett email küldés
- [ ] Email template testreszabás
- [ ] Audit log bővítése
- [ ] API endpoint-ok

A rendszer teljes mértékben működőképes és készen áll a használatra! 🚀
