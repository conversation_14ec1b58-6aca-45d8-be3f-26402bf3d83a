<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ServiceProvider;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ServiceProviderController extends Controller
{
    /**
     * Lista megjelenítése
     */
    public function index(Request $request)
    {
        $query = ServiceProvider::query();

        // Keresés
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Aktív/inaktív szűré<PERSON>
        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $serviceProviders = $query
            ->withCount('webhooks')
            ->orderBy('name')
            ->paginate(15)
            ->withQueryString();

        return Inertia::render('admin/service-providers/index', [
            'serviceProviders' => $serviceProviders,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Új szerviz form
     */
    public function create()
    {
        return Inertia::render('admin/service-providers/create');
    }

    /**
     * Szerviz mentése
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'postal_code' => 'required|string|max:10',
            'street' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'emails' => 'nullable|array',
            'emails.*' => 'email|max:255',
            'is_active' => 'boolean',
        ]);

        // Teljes cím generálása
        $validated['address'] = trim("{$validated['postal_code']} {$validated['city']}, {$validated['street']}");

        $serviceProvider = ServiceProvider::create($validated);

        return redirect()
            ->route('admin.service-providers.index')
            ->with('success', 'Szerviz sikeresen létrehozva!');
    }

    /**
     * Szerviz megtekintése
     */
    public function show(ServiceProvider $serviceProvider)
    {
        $serviceProvider->load(['webhooks' => function ($query) {
            $query->orderBy('name');
        }]);

        return Inertia::render('admin/service-providers/show', [
            'serviceProvider' => $serviceProvider,
        ]);
    }

    /**
     * Szerviz szerkesztése
     */
    public function edit(ServiceProvider $serviceProvider)
    {
        return Inertia::render('admin/service-providers/edit', [
            'serviceProvider' => $serviceProvider,
        ]);
    }

    /**
     * Szerviz frissítése
     */
    public function update(Request $request, ServiceProvider $serviceProvider)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'postal_code' => 'required|string|max:10',
            'street' => 'required|string|max:255',
            'city' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'emails' => 'nullable|array',
            'emails.*' => 'email|max:255',
            'is_active' => 'boolean',
        ]);

        // Teljes cím generálása
        $validated['address'] = trim("{$validated['postal_code']} {$validated['city']}, {$validated['street']}");

        $serviceProvider->update($validated);

        return redirect()
            ->route('admin.service-providers.index')
            ->with('success', 'Szerviz sikeresen frissítve!');
    }

    /**
     * Szerviz törlése (soft delete)
     */
    public function destroy(ServiceProvider $serviceProvider)
    {
        $serviceProvider->update(['is_active' => false]);

        return redirect()
            ->route('admin.service-providers.index')
            ->with('success', 'Szerviz sikeresen inaktiválva!');
    }

    /**
     * Szerviz aktiválása
     */
    public function activate(ServiceProvider $serviceProvider)
    {
        $serviceProvider->update(['is_active' => true]);

        return redirect()
            ->route('admin.service-providers.index')
            ->with('success', 'Szerviz sikeresen aktiválva!');
    }

    /**
     * Excel export
     */
    public function export()
    {
        // TODO: Excel export implementálása
        return response()->json(['message' => 'Excel export hamarosan elérhető']);
    }

    /**
     * Excel import
     */
    public function import(Request $request)
    {
        // TODO: Excel import implementálása
        return response()->json(['message' => 'Excel import hamarosan elérhető']);
    }

    /**
     * API endpoint - aktív szervizek listája
     */
    public function apiIndex()
    {
        $serviceProviders = ServiceProvider::active()
            ->select('id', 'name', 'city', 'phone', 'emails')
            ->orderBy('name')
            ->get();

        return response()->json($serviceProviders);
    }
}
