import { useState } from 'react';
import { router } from '@inertiajs/react';
import { 
    Building2, 
    Car, 
    Mail, 
    Phone, 
    Hash, 
    Briefcase, 
    User, 
    Calendar,
    CheckCircle,
    XCircle,
    AlertTriangle
} from 'lucide-react';

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
    AlertDialog, 
    AlertDialogAction, 
    AlertDialogCancel, 
    AlertDialogContent, 
    AlertDialogDescription, 
    AlertDialogFooter, 
    AlertDialogHeader, 
    AlertDialogTitle 
} from '@/components/ui/alert-dialog';

type User = {
    id: number;
    name: string;
    email: string;
    company_type: 'posta' | 'partner';
    company_name?: string;
    tax_number?: string;
    position?: string;
    phone?: string;
    created_at: string;
    contact?: {
        id: number;
        name: string;
        username: string;
    };
    org_unit?: {
        id: number;
        name: string;
    };
    garage?: {
        id: number;
        name: string;
    };
};

type Props = {
    user: User;
    open: boolean;
    onClose: () => void;
    canManageUsers: boolean;
    canManagePostaUsers: boolean;
};

export function UserApprovalModal({ user, open, onClose, canManageUsers, canManagePostaUsers }: Props) {
    const [rejectionReason, setRejectionReason] = useState('');
    const [showRejectDialog, setShowRejectDialog] = useState(false);
    const [showApproveDialog, setShowApproveDialog] = useState(false);
    const [isProcessing, setIsProcessing] = useState(false);

    const canManage = (user.company_type === 'posta' && canManagePostaUsers) || 
                     (user.company_type === 'partner' && canManageUsers);

    const handleApprove = () => {
        setIsProcessing(true);
        router.post(`/admin/user-approval/${user.id}/approve`, {}, {
            onSuccess: () => {
                onClose();
                setShowApproveDialog(false);
            },
            onFinish: () => setIsProcessing(false)
        });
    };

    const handleReject = () => {
        if (!rejectionReason.trim() || rejectionReason.length < 10) {
            return;
        }

        setIsProcessing(true);
        router.post(`/admin/user-approval/${user.id}/reject`, {
            reason: rejectionReason
        }, {
            onSuccess: () => {
                onClose();
                setShowRejectDialog(false);
                setRejectionReason('');
            },
            onFinish: () => setIsProcessing(false)
        });
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('hu-HU', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const getCompanyTypeBadge = (companyType: 'posta' | 'partner') => {
        if (companyType === 'posta') {
            return <Badge variant="default" className="bg-blue-100 text-blue-800">Magyar Posta Zrt.</Badge>;
        }
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Partner cég</Badge>;
    };

    return (
        <>
            <Dialog open={open} onOpenChange={onClose}>
                <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                            <User className="h-5 w-5" />
                            Felhasználó részletei
                        </DialogTitle>
                    </DialogHeader>

                    <div className="space-y-6">
                        {/* Alapadatok */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label className="text-sm font-medium text-gray-500">Név</Label>
                                <p className="text-lg font-medium">{user.name}</p>
                            </div>
                            <div>
                                <Label className="text-sm font-medium text-gray-500">Email cím</Label>
                                <div className="flex items-center gap-2">
                                    <Mail className="h-4 w-4 text-gray-400" />
                                    <p>{user.email}</p>
                                </div>
                            </div>
                        </div>

                        {/* Cég típus */}
                        <div>
                            <Label className="text-sm font-medium text-gray-500">Cég típusa</Label>
                            <div className="mt-1">
                                {getCompanyTypeBadge(user.company_type)}
                            </div>
                        </div>

                        {/* Posta specifikus adatok */}
                        {user.company_type === 'posta' && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h3 className="font-medium text-blue-900 mb-3 flex items-center gap-2">
                                    <Building2 className="h-4 w-4" />
                                    Magyar Posta adatok
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {user.contact && (
                                        <div>
                                            <Label className="text-sm font-medium text-blue-700">Felhasználónév</Label>
                                            <p className="text-blue-900">{user.contact.username}</p>
                                        </div>
                                    )}
                                    {user.org_unit && (
                                        <div>
                                            <Label className="text-sm font-medium text-blue-700">Szervezeti egység</Label>
                                            <p className="text-blue-900">{user.org_unit.name}</p>
                                        </div>
                                    )}
                                    {user.garage && (
                                        <div className="md:col-span-2">
                                            <Label className="text-sm font-medium text-blue-700">Garázs</Label>
                                            <div className="flex items-center gap-2">
                                                <Car className="h-4 w-4 text-blue-600" />
                                                <p className="text-blue-900">{user.garage.name}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Partner specifikus adatok */}
                        {user.company_type === 'partner' && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h3 className="font-medium text-green-900 mb-3 flex items-center gap-2">
                                    <Building2 className="h-4 w-4" />
                                    Partner cég adatok
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    {user.company_name && (
                                        <div>
                                            <Label className="text-sm font-medium text-green-700">Cégnév</Label>
                                            <p className="text-green-900">{user.company_name}</p>
                                        </div>
                                    )}
                                    {user.tax_number && (
                                        <div>
                                            <Label className="text-sm font-medium text-green-700">Adószám</Label>
                                            <div className="flex items-center gap-2">
                                                <Hash className="h-4 w-4 text-green-600" />
                                                <p className="text-green-900">{user.tax_number}</p>
                                            </div>
                                        </div>
                                    )}
                                    {user.position && (
                                        <div>
                                            <Label className="text-sm font-medium text-green-700">Munkakör</Label>
                                            <div className="flex items-center gap-2">
                                                <Briefcase className="h-4 w-4 text-green-600" />
                                                <p className="text-green-900">{user.position}</p>
                                            </div>
                                        </div>
                                    )}
                                    {user.phone && (
                                        <div>
                                            <Label className="text-sm font-medium text-green-700">Telefonszám</Label>
                                            <div className="flex items-center gap-2">
                                                <Phone className="h-4 w-4 text-green-600" />
                                                <p className="text-green-900">{user.phone}</p>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Regisztráció dátuma */}
                        <div>
                            <Label className="text-sm font-medium text-gray-500">Regisztráció időpontja</Label>
                            <div className="flex items-center gap-2 mt-1">
                                <Calendar className="h-4 w-4 text-gray-400" />
                                <p>{formatDate(user.created_at)}</p>
                            </div>
                        </div>

                        {/* Műveletek */}
                        {canManage && (
                            <div className="flex justify-end gap-3 pt-4 border-t">
                                <Button
                                    variant="outline"
                                    onClick={() => setShowRejectDialog(true)}
                                    disabled={isProcessing}
                                    className="text-red-600 border-red-200 hover:bg-red-50"
                                >
                                    <XCircle className="h-4 w-4 mr-2" />
                                    Elutasítás
                                </Button>
                                <Button
                                    onClick={() => setShowApproveDialog(true)}
                                    disabled={isProcessing}
                                    className="bg-green-600 hover:bg-green-700"
                                >
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Jóváhagyás
                                </Button>
                            </div>
                        )}

                        {!canManage && (
                            <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                <div className="flex items-center gap-2 text-amber-800">
                                    <AlertTriangle className="h-5 w-5" />
                                    <span className="font-medium">Nincs jogosultsága</span>
                                </div>
                                <p className="text-amber-700 mt-1">
                                    Nem rendelkezik jogosultsággal ennek a felhasználónak a jóváhagyásához.
                                </p>
                            </div>
                        )}
                    </div>
                </DialogContent>
            </Dialog>

            {/* Jóváhagyás megerősítő dialog */}
            <AlertDialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5 text-green-600" />
                            Felhasználó jóváhagyása
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            Biztosan jóvá szeretné hagyni <strong>{user.name}</strong> regisztrációját?
                            <br />
                            A felhasználó aktiválásra kerül és email értesítést kap.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isProcessing}>Mégse</AlertDialogCancel>
                        <AlertDialogAction 
                            onClick={handleApprove}
                            disabled={isProcessing}
                            className="bg-green-600 hover:bg-green-700"
                        >
                            {isProcessing ? 'Feldolgozás...' : 'Jóváhagyás'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Elutasítás dialog */}
            <AlertDialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <XCircle className="h-5 w-5 text-red-600" />
                            Felhasználó elutasítása
                        </AlertDialogTitle>
                        <AlertDialogDescription>
                            <strong>{user.name}</strong> regisztrációjának elutasítása.
                            <br />
                            A felhasználó törlésre kerül és email értesítést kap az indoklással.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    
                    <div className="space-y-2">
                        <Label htmlFor="rejection-reason">Elutasítás indoklása *</Label>
                        <Textarea
                            id="rejection-reason"
                            placeholder="Kérjük, adja meg az elutasítás részletes indoklását..."
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                            rows={4}
                            disabled={isProcessing}
                        />
                        {rejectionReason.length < 10 && rejectionReason.length > 0 && (
                            <p className="text-sm text-red-500">
                                Az indoklásnak legalább 10 karakter hosszúnak kell lennie.
                            </p>
                        )}
                    </div>

                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isProcessing}>Mégse</AlertDialogCancel>
                        <AlertDialogAction 
                            onClick={handleReject}
                            disabled={isProcessing || !rejectionReason.trim() || rejectionReason.length < 10}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            {isProcessing ? 'Feldolgozás...' : 'Elutasítás'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </>
    );
}
