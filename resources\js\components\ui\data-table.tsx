// components/ui/data-table.tsx

import {
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  useReactTable,
  ColumnDef,
  SortingState,
  getFilteredRowModel,
  getPaginationRowModel,
  PaginationState,
} from '@tanstack/react-table'
import * as React from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { 
  ArrowUpDown, 
  ArrowDown, 
  ArrowUp, 
  CheckCircle, 
  Trash2, 
  Pencil, 
  Search, 
  ChevronLeft, 
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Download
} from 'lucide-react'
import { cn } from '@/lib/utils'


// --- Add type support for columnDef.meta.align and sortable ---
type ColumnAlign = 'left' | 'right' | 'center'
declare module '@tanstack/react-table' {
  interface ColumnMeta<TData extends unknown, TValue> {
    align?: ColumnAlign
    sortable?: boolean
  }
}

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[]
  data: TData[]
  onDelete?: (row: TData) => void
  onEdit?: (row: TData) => void
  globalFilter?: string
  exportFileName?: string
  pageSize?: number
  showExportButton?: boolean
  onExport?: (data: TData[]) => void
}

export function DataTable<TData, TValue>({ 
  columns, 
  data, 
  onDelete, 
  onEdit, 
  globalFilter: externalGlobalFilter,
  exportFileName = 'export',
  pageSize = 10,
  showExportButton = false,
  onExport,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [globalFilter, setGlobalFilter] = React.useState('')
  const [pagination, setPagination] = React.useState<PaginationState>({
    pageIndex: 0,
    pageSize: pageSize,
  })

  // Use external filter if provided, otherwise use internal
  const effectiveGlobalFilter = externalGlobalFilter !== undefined ? externalGlobalFilter : globalFilter

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    state: {
      sorting,
      globalFilter: effectiveGlobalFilter,
      pagination,
    },
    onSortingChange: setSorting,
    onGlobalFilterChange: externalGlobalFilter !== undefined ? undefined : setGlobalFilter,
    onPaginationChange: setPagination,
  })

  return (
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-gray-100 dark:border-slate-600 overflow-hidden">
      {/* Export button */}
      {showExportButton && onExport && (
        <div className="p-4 border-b border-gray-100 dark:border-slate-600">          
          <div className="flex justify-end">
            <Button
              onClick={() => {
                const exportData = table.getFilteredRowModel().rows.map(row => row.original)
                onExport(exportData)
              }}
              variant="outline"
              size="sm"
              className="flex items-center gap-2 border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:hover:border-slate-400"
            >
              <Download className="h-4 w-4" />
              Excel export
            </Button>
          </div>
        </div>
      )}

      {/* Search input */}
      <div className="overflow-x-auto">
        <Table className="w-full">
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b border-gray-100 dark:border-slate-600 hover:bg-transparent">
                {headerGroup.headers.map(header => {
                  const isSortable = header.column.columnDef.meta?.sortable ?? header.column.getCanSort()
                  const sortDirection = header.column.getIsSorted()
                  return (
                    <TableHead
                      key={header.id}
                      onClick={isSortable ? header.column.getToggleSortingHandler() : undefined}
                      className={cn(
                        'px-4 py-3 text-left text-xs font-semibold text-gray-600 dark:text-slate-200 uppercase tracking-wider bg-gray-50/50 dark:bg-slate-700/50',
                        isSortable && 'cursor-pointer hover:bg-gray-100/50 dark:hover:bg-slate-600/50 transition-colors duration-150'
                      )}
                    >
                      <div className="flex items-center gap-2">
                        {flexRender(header.column.columnDef.header, header.getContext())}
                        {isSortable && (
                          <div className="flex items-center">
                            {sortDirection === 'asc' ? (
                              <ArrowUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                            ) : sortDirection === 'desc' ? (
                              <ArrowDown className="h-4 w-4 text-green-600 dark:text-green-400" />
                            ) : (
                              <ArrowUpDown className="h-4 w-4 text-gray-400 dark:text-slate-500 group-hover:text-gray-600 dark:group-hover:text-slate-300" />
                            )}
                          </div>
                        )}
                      </div>
                    </TableHead>
                  )
                })}
                {(onEdit || onDelete) && (
                  <TableHead className="px-4 py-3 text-center text-xs font-semibold text-gray-600 dark:text-slate-200 uppercase tracking-wider bg-gray-50/50 dark:bg-slate-700/50 w-20">
                    
                  </TableHead>
                )}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row, index) => (
                <TableRow 
                  key={row.id} 
                  className={cn(
                    'border-b border-gray-50 dark:border-slate-600/50 hover:bg-gray-50/50 dark:hover:bg-slate-700/50 transition-colors duration-150',
                    index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-gray-50/25 dark:bg-slate-700/30'
                  )}
                >
                  {row.getVisibleCells().map(cell => {
                    const alignment = cell.column.columnDef.meta?.align || 'left'
                    const isArray = Array.isArray(cell.getValue())
                    return (
                      <TableCell
                        key={cell.id}
                        className={cn(
                          'px-4 py-3 text-sm text-gray-900 dark:text-slate-100',
                          alignment === 'right' ? 'text-right' : alignment === 'center' ? 'text-center' : 'text-left'
                        )}
                      >
                        {isArray ? (
                          (cell.getValue() as string[]).length === 1 && (cell.getValue() as string[])[0] === '-' ? (
                            <span className="text-muted-foreground dark:text-slate-400 text-xs">-</span>
                          ) : (
                            <div className="flex flex-wrap gap-1">
                              {(cell.getValue() as string[]).map((item, i) => (
                                <span
                                  key={i}
                                  className="inline-flex items-center gap-1 rounded-full bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700/40 text-green-700 dark:text-green-300 px-2 py-0.5 text-xs font-medium"
                                >
                                  <CheckCircle className="h-3 w-3 text-green-600 dark:text-green-400" />
                                  {item}
                                </span>
                              ))}
                            </div>
                          )
                        ) : (
                          flexRender(cell.column.columnDef.cell, cell.getContext())
                        )}
                      </TableCell>
                    )
                  })}
                  {(onEdit || onDelete) && (
                    <TableCell className="px-4 py-3 text-center">
                      <div className="flex items-center justify-center gap-1">
                        {onEdit && (
                          <button
                            onClick={() => onEdit(row.original)}
                            className="inline-flex items-center justify-center w-7 h-7 rounded-md border border-gray-200 dark:border-slate-500 bg-white dark:bg-slate-700 text-gray-600 dark:text-slate-200 hover:bg-gray-50 dark:hover:bg-slate-600 hover:text-green-600 dark:hover:text-green-400 hover:border-green-200 dark:hover:border-green-500 transition-all duration-150"
                            title="Szerkesztés"
                          >
                            <Pencil className="h-3.5 w-3.5" />
                          </button>
                        )}
                        {onDelete && (
                          <button
                            onClick={() => onDelete(row.original)}
                            className="inline-flex items-center justify-center w-7 h-7 rounded-md border border-gray-200 dark:border-slate-500 bg-white dark:bg-slate-700 text-gray-600 dark:text-slate-200 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400 hover:border-red-200 dark:hover:border-red-500 transition-all duration-150"
                            title="Törlés"
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                          </button>
                        )}
                      </div>
                    </TableCell>
                  )}
                </TableRow>
              ))
            ) : (
              <TableRow className="hover:bg-transparent">
                <TableCell 
                  colSpan={columns.length + ((onEdit || onDelete) ? 1 : 0)} 
                  className="text-center py-8 text-gray-500 dark:text-slate-400"
                >
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-10 h-10 bg-gray-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                      <Search className="h-5 w-5 text-gray-400 dark:text-slate-500" />
                    </div>
                    <p className="text-sm text-gray-500 dark:text-slate-400">Nincs megjeleníthető adat</p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between px-4 py-3 border-t border-gray-100 dark:border-slate-600">
        <div className="flex items-center gap-2">
          <p className="text-sm text-gray-700 dark:text-slate-200">
            Összesen {table.getFilteredRowModel().rows.length} elem közül{' '}
            {table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}-
            {Math.min(
              (table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize,
              table.getFilteredRowModel().rows.length
            )}{' '}
            megjelenítése
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(0)}
              disabled={!table.getCanPreviousPage()}
              className="border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:hover:border-slate-400 disabled:dark:bg-slate-800 disabled:dark:text-slate-400 disabled:dark:border-slate-600"
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:hover:border-slate-400 disabled:dark:bg-slate-800 disabled:dark:text-slate-400 disabled:dark:border-slate-600"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="flex items-center gap-1 text-sm text-gray-700 dark:text-slate-200">
              <div>Oldal</div>
              <strong>
                {table.getState().pagination.pageIndex + 1} / {table.getPageCount()}
              </strong>
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:hover:border-slate-400 disabled:dark:bg-slate-800 disabled:dark:text-slate-400 disabled:dark:border-slate-600"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.setPageIndex(table.getPageCount() - 1)}
              disabled={!table.getCanNextPage()}
              className="border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 dark:hover:bg-slate-600 dark:hover:border-slate-400 disabled:dark:bg-slate-800 disabled:dark:text-slate-400 disabled:dark:border-slate-600"
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
          
          <select
            value={table.getState().pagination.pageSize}
            onChange={e => {
              table.setPageSize(Number(e.target.value))
            }}
            className="px-2 py-1 border border-gray-300 dark:border-slate-500 dark:bg-slate-700 dark:text-slate-200 rounded text-sm focus:outline-none focus:ring-2 focus:ring-green-500 dark:focus:ring-green-400"
          >
            {[10, 20, 30, 40, 50].map(pageSize => (
              <option key={pageSize} value={pageSize}>
                {pageSize} elem
              </option>
            ))}
          </select>
        </div>
      </div>
    </div>
  )
}
