import { Button } from '@/components/ui/button';
import { EnhancedPermissionSelector } from '@/components/ui/enhanced-permission-selector';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { type Permission } from '@/utils/permissions';
import { useForm } from '@inertiajs/react';
import { BadgeDollarSign, UserCog } from 'lucide-react';
import { useEffect } from 'react';

type RolePermission = {
    name: string;
};

type Role = {
    id?: number;
    name: string;
    approval_limit: number | null;
    permissions: RolePermission[];
};

type Props = {
    role: Role | null;
    permissions: Permission[];
    onClose: () => void;
};

export default function RoleForm({ role, permissions, onClose }: Props) {
    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: role?.name || '',
        approval_limit: role?.approval_limit ?? '',
        permissions: role?.permissions?.map((p: RolePermission) => p.name) || [],
    });

    useEffect(() => {
        return () => reset();
    }, []);

    function handleSubmit(e: React.FormEvent) {
        e.preventDefault();
        const action = role ? put : post;
        const url = role ? `/admin/roles/${role.id}` : '/admin/roles';
        action(url, {
            onSuccess: () => onClose(),
        });
    }

    function handlePermissionsChange(newPermissions: string[]) {
        setData('permissions', newPermissions);
    }

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
                <Label htmlFor="name">Név</Label>
                <div className="relative">
                    <UserCog className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                    <Input id="name" className="pl-10" value={data.name} onChange={(e) => setData('name', e.target.value)} />
                </div>
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
            </div>

            <div className="space-y-2">
                <Label htmlFor="limit">Jóváhagyási összeghatár (opcionális)</Label>
                <div className="relative">
                    <BadgeDollarSign className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                    <Input
                        id="limit"
                        type="number"
                        className="pl-10"
                        value={data.approval_limit}
                        onChange={(e) => setData('approval_limit', e.target.value)}
                    />
                </div>
                {errors.approval_limit && <p className="text-sm text-red-500">{errors.approval_limit}</p>}
            </div>

            <Separator />

            <EnhancedPermissionSelector
                availablePermissions={permissions}
                selectedPermissionNames={data.permissions}
                onChange={handlePermissionsChange}
                disabled={processing}
            />

            {errors.permissions && <p className="text-sm text-red-500">{errors.permissions}</p>}

            <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {role ? 'Mentés' : 'Létrehozás'}
                </Button>
            </div>
        </form>
    );
}
