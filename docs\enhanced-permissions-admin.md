# Fejlett Permissions Admin Felület

## 🎯 Áttekintés

Az új permissions admin felület lehetővé teszi az adminisztrátorok számára, hogy teljes körűen kezeljék a jogosultságokat az új mező<PERSON>kel együtt:

### ✨ Főbb Funkciók
- **Magyar me<PERSON>**: Érthető nevek technikai nevek mellett
- **Kategória kezelés**: Dinamikus kategóriák a permissions táblából
- **Veszélyes jogosultságok**: Vizuális jelölés és figyelmeztetések
- **Rendezési sorrend**: Egyedi sorrendbe állítás
- **Kategória felvétel**: SingleSelect komponenssel új kategóriák

## 🗄️ Backend Módosítások

### PermissionController Frissítése

#### Index Metódus
```php
public function index()
{
    return Inertia::render('admin/permissions/index', [
        'permissions' => Permission::with('roles:id,name')
            ->select('id', 'name', 'display_name', 'description', 'category', 'is_dangerous', 'sort_order')
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get(),
        'categories' => Permission::distinct()
            ->whereNotNull('category')
            ->pluck('category')
            ->filter()
            ->sort()
            ->values()
    ]);
}
```

#### Store/Update Validáció
```php
$validated = $request->validate([
    'name' => 'required|string|max:255|unique:permissions,name',
    'display_name' => 'nullable|string|max:255',
    'description' => 'nullable|string|max:1000',
    'category' => 'required|string|max:255',
    'is_dangerous' => 'boolean',
    'sort_order' => 'integer|min:0',
]);
```

## 🎨 Frontend Komponensek

### Permissions Lista (index.tsx)

#### Frissített Oszlopok
```tsx
const columns: ColumnDef<Permission>[] = [
    {
        header: 'Technikai név',
        accessorKey: 'name',
        cell: ({ row }) => (
            <code className="text-xs bg-gray-100 px-2 py-1 rounded">
                {row.original.name}
            </code>
        ),
    },
    {
        header: 'Magyar név',
        accessorKey: 'display_name',
        cell: ({ row }) => (
            <div className="flex items-center gap-2">
                <span>{row.original.display_name || row.original.name}</span>
                {row.original.is_dangerous && (
                    <span className="text-amber-500">⚠️</span>
                )}
            </div>
        ),
    },
    {
        header: 'Kategória',
        accessorKey: 'category',
        cell: ({ row }) => (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {row.original.category}
            </span>
        ),
    },
    // ... további oszlopok
];
```

### Permission Form (PermissionForm.tsx)

#### Új Mezők
```tsx
// Technikai név
<Input
    placeholder="pl. manage_users"
    value={data.name}
    onChange={(e) => setData('name', e.target.value)}
/>

// Magyar megnevezés
<Input
    placeholder="pl. Felhasználók kezelése"
    value={data.display_name}
    onChange={(e) => setData('display_name', e.target.value)}
/>

// Kategória (SingleSelect allowCustomValue-val)
<SingleSelect
    options={categoryOptions}
    value={data.category}
    onChange={(value) => setData('category', value)}
    allowCustomValue={true}
/>

// Rendezési sorrend
<Input
    type="number"
    min="0"
    value={data.sort_order}
    onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
/>

// Veszélyes jogosultság
<input
    type="checkbox"
    checked={data.is_dangerous}
    onChange={(e) => setData('is_dangerous', e.target.checked)}
/>
```

## 🎮 Felhasználói Élmény

### Permissions Lista

#### Előtte (Régi)
```
┌─────────────────────────────────────────────────────────┐
│ Név                    │ Szerepkörök    │ Leírás         │
├─────────────────────────────────────────────────────────┤
│ access_system_admin... │ SystemAdmin    │ Hozzáférés...  │
│ manage_users           │ SystemAdmin    │ Felhasználók...│
│ submit_repair_request  │ User           │ Javítási...    │
└─────────────────────────────────────────────────────────┘
```

#### Utána (Új)
```
┌──────────────────────────────────────────────────────────────────────────────────────┐
│ Technikai név          │ Magyar név                    │ Kategória        │ Leírás    │
├──────────────────────────────────────────────────────────────────────────────────────┤
│ access_system_admin... │ Rendszer admin funkciók ⚠️   │ [system]         │ Hozzáf... │
│ manage_users           │ Partner felhasználók kezelése │ [user_management]│ Partner...│
│ submit_repair_request  │ Javítási kérelem beküldése    │ [repair_mgmt]    │ Új jav... │
└──────────────────────────────────────────────────────────────────────────────────────┘
```

### Permission Form

#### Előtte (Régi)
```
┌─────────────────────────────────┐
│ Jogosultság neve:               │
│ [manage_users____________]      │
│                                 │
│ Leírás:                         │
│ [________________________]     │
│ [________________________]     │
│                                 │
│           [Mégse] [Mentés]      │
└─────────────────────────────────┘
```

#### Utána (Új)
```
┌─────────────────────────────────────────────────────────────────────┐
│ Jogosultság szerkesztése                                            │
│                                                                     │
│ Technikai név:              │ Magyar megnevezés:                    │
│ [manage_users_______]       │ [Partner felhasználók kezelése___]    │
│                                                                     │
│ Kategória:                  │ Rendezési sorrend:                    │
│ [user_management ▼] + új    │ [200___]                              │
│                                                                     │
│ Leírás:                                                             │
│ [Partner felhasználók jóváhagyása és kezelése______________]        │
│ [_____________________________________________________________]      │
│                                                                     │
│ ☑ Veszélyes jogosultság                                             │
│ ⚠️ Ez a jogosultság veszélyesként lesz megjelölve...                │
│                                                                     │
│ ─────────────────────────────────────────────────────────────────── │
│                                           [Mégse] [Mentés]          │
└─────────────────────────────────────────────────────────────────────┘
```

## 🔧 Kategória Kezelés

### Dinamikus Kategóriák
A kategóriák nem statikusan vannak definiálva, hanem a permissions táblából kerülnek lekérdezésre:

```php
'categories' => Permission::distinct()
    ->whereNotNull('category')
    ->pluck('category')
    ->filter()
    ->sort()
    ->values()
```

### SingleSelect allowCustomValue
A kategória mező SingleSelect komponenst használ `allowCustomValue={true}` beállítással:

```tsx
<SingleSelect
    options={categoryOptions}
    value={data.category}
    onChange={(value) => setData('category', value)}
    placeholder="Válassz kategóriát..."
    allowCustomValue={true}  // Új kategória felvétele
    disabled={processing}
/>
```

#### Működés:
1. **Meglévő kategóriák**: Dropdown listából választható
2. **Új kategória**: Begépelhető, automatikusan hozzáadódik
3. **Dinamikus frissítés**: Új kategória mentés után megjelenik a listában

## 🛡️ Biztonsági Funkciók

### Veszélyes Jogosultságok
```tsx
// Checkbox a veszélyes jelöléshez
<input
    type="checkbox"
    checked={data.is_dangerous}
    onChange={(e) => setData('is_dangerous', e.target.checked)}
/>

// Figyelmeztetés megjelenítése
{data.is_dangerous && (
    <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
        <p className="text-amber-700 text-sm">
            ⚠️ Ez a jogosultság veszélyesként lesz megjelölve. 
            Csak megbízható felhasználóknak adja meg!
        </p>
    </div>
)}
```

### Lista Megjelenítés
```tsx
// Veszélyes jogosultság jelölése a listában
{row.original.is_dangerous && (
    <span className="text-amber-500" title="Veszélyes jogosultság">⚠️</span>
)}
```

## 📊 Rendezési Sorrend

### Automatikus Rendezés
```php
// Backend: sort_order szerint rendezés
->orderBy('sort_order')
->orderBy('name')
```

### Form Mező
```tsx
<Input
    type="number"
    min="0"
    value={data.sort_order}
    onChange={(e) => setData('sort_order', parseInt(e.target.value) || 0)}
    placeholder="0"
/>
```

## 🚀 Előnyök

### 1. 📈 Jobb Áttekinthetőség
- **Magyar megnevezések**: Azonnal érthető funkciók
- **Kategória badge-ek**: Vizuális csoportosítás
- **Veszélyes jelölések**: Biztonsági figyelmeztetések

### 2. 🔧 Könnyű Kezelés
- **Új kategóriák**: Egyszerű felvétel SingleSelect-tel
- **Rendezési sorrend**: Egyedi sorrendbe állítás
- **Batch szerkesztés**: Gyors módosítások

### 3. 🛡️ Fokozott Biztonság
- **Veszélyes jogosultságok**: Vizuális jelölés
- **Figyelmeztetések**: Automatikus üzenetek
- **Audit trail**: Világos nyomon követés

### 4. 🎯 Felhasználóbarát
- **Intuitív form**: Logikus mezőelrendezés
- **Placeholder szövegek**: Segítő információk
- **Validációs üzenetek**: Azonnali visszajelzés

## 📋 Használati Útmutató

### Admin Számára

#### 1. Permission Létrehozása
1. **Admin** → **Jogosultságok** → **Új jogosultság**
2. **Technikai név**: Egyedi azonosító (pl. `manage_partners`)
3. **Magyar megnevezés**: Érthető név (pl. `Partner cégek kezelése`)
4. **Kategória**: Válassz meglévőt vagy írj újat
5. **Leírás**: Részletes funkció leírás
6. **Veszélyes**: Jelöld be ha szükséges
7. **Rendezési sorrend**: Pozíció megadása

#### 2. Kategória Felvétele
1. **Kategória mező**: Kattints a dropdown-ra
2. **Új kategória**: Gépeld be az új nevet
3. **Enter**: Automatikusan hozzáadódik
4. **Mentés**: Új kategória elérhető lesz

#### 3. Veszélyes Jogosultságok
- **Jelölés**: Checkbox bejelölése
- **Figyelmeztetés**: Sárga panel megjelenése
- **Lista**: ⚠️ ikon megjelenítése

A rendszer most teljes körűen támogatja a fejlett permission kezelést! 🎉
