import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Activity, ArrowLeft, Building2, Edit, Mail, MapPin, Phone, Plus, Settings, Webhook } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface ServiceProviderWebhook {
    id: number;
    name: string;
    event_type: string;
    url: string;
    method: string;
    is_active: boolean;
    last_called_at?: string;
    last_status?: string;
}

interface ServiceProvider {
    id: number;
    name: string;
    postal_code: string;
    street: string;
    city: string;
    address: string;
    phone?: string;
    emails?: string[];
    is_active: boolean;
    webhooks: ServiceProviderWebhook[];
    created_at: string;
    updated_at: string;
}

interface Props {
    serviceProvider: ServiceProvider;
}

export default function ShowServiceProvider({ serviceProvider }: Props) {
    const getStatusBadge = (status?: string) => {
        switch (status) {
            case 'success':
                return (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                        Sikeres
                    </Badge>
                );
            case 'failed':
                return <Badge variant="destructive">Sikertelen</Badge>;
            case 'error':
                return <Badge variant="destructive">Hiba</Badge>;
            default:
                return <Badge variant="secondary">Nincs adat</Badge>;
        }
    };

    return (
        <AppLayout>
            <Head title={serviceProvider.name} />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <Button variant="outline" size="sm" asChild>
                            <Link href={route('admin.service-providers.index')}>
                                <ArrowLeft className="mr-2 h-4 w-4" />
                                Vissza
                            </Link>
                        </Button>
                        <div>
                            <h1 className="flex items-center gap-2 text-2xl font-bold text-gray-900">
                                <Building2 className="h-6 w-6 text-blue-600" />
                                {serviceProvider.name}
                            </h1>
                            <p className="text-gray-600">Szerviz részletes adatai és webhook beállításai</p>
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        <Badge variant={serviceProvider.is_active ? 'default' : 'secondary'}>{serviceProvider.is_active ? 'Aktív' : 'Inaktív'}</Badge>
                        <Button asChild>
                            <Link href={route('admin.service-providers.edit', serviceProvider.id)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Szerkesztés
                            </Link>
                        </Button>
                    </div>
                </div>

                <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
                    {/* Alapadatok */}
                    <div className="space-y-6 lg:col-span-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Building2 className="h-5 w-5" />
                                    Alapadatok
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-500">Szerviz neve</label>
                                    <p className="text-lg font-medium">{serviceProvider.name}</p>
                                </div>

                                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                                    <div>
                                        <label className="flex items-center gap-1 text-sm font-medium text-gray-500">
                                            <MapPin className="h-4 w-4" />
                                            Cím
                                        </label>
                                        <div className="space-y-1">
                                            <p>
                                                {serviceProvider.postal_code} {serviceProvider.city}
                                            </p>
                                            <p className="text-gray-600">{serviceProvider.street}</p>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        {serviceProvider.phone && (
                                            <div>
                                                <label className="flex items-center gap-1 text-sm font-medium text-gray-500">
                                                    <Phone className="h-4 w-4" />
                                                    Telefonszám
                                                </label>
                                                <p>{serviceProvider.phone}</p>
                                            </div>
                                        )}

                                        {serviceProvider.emails && serviceProvider.emails.length > 0 && (
                                            <div>
                                                <label className="flex items-center gap-1 text-sm font-medium text-gray-500">
                                                    <Mail className="h-4 w-4" />
                                                    Email címek
                                                </label>
                                                <div className="space-y-1">
                                                    {serviceProvider.emails.map((email, index) => (
                                                        <p key={index} className="text-blue-600">
                                                            <a href={`mailto:${email}`}>{email}</a>
                                                        </p>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Webhookok */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <CardTitle className="flex items-center gap-2">
                                        <Webhook className="h-5 w-5" />
                                        Webhookok ({serviceProvider.webhooks.length})
                                    </CardTitle>
                                    <Button size="sm" asChild>
                                        <Link href={route('admin.service-providers.webhooks.index', serviceProvider.id)}>
                                            <Settings className="mr-2 h-4 w-4" />
                                            Kezelés
                                        </Link>
                                    </Button>
                                </div>
                            </CardHeader>
                            <CardContent>
                                {serviceProvider.webhooks.length > 0 ? (
                                    <div className="space-y-3">
                                        {serviceProvider.webhooks.map((webhook) => (
                                            <div key={webhook.id} className="flex items-center justify-between rounded-lg border p-3">
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-2">
                                                        <h4 className="font-medium">{webhook.name}</h4>
                                                        <Badge variant={webhook.is_active ? 'default' : 'secondary'} className="text-xs">
                                                            {webhook.is_active ? 'Aktív' : 'Inaktív'}
                                                        </Badge>
                                                    </div>
                                                    <p className="text-sm text-gray-500">{webhook.event_type}</p>
                                                    <p className="font-mono text-xs text-gray-400">
                                                        {webhook.method} {webhook.url}
                                                    </p>
                                                </div>
                                                <div className="flex items-center gap-2">
                                                    {getStatusBadge(webhook.last_status)}
                                                    {webhook.last_called_at && (
                                                        <div className="text-xs text-gray-500">
                                                            {new Date(webhook.last_called_at).toLocaleString('hu-HU')}
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="py-8 text-center">
                                        <Webhook className="mx-auto mb-4 h-12 w-12 text-gray-300" />
                                        <p className="mb-4 text-gray-500">Még nincsenek webhookok beállítva</p>
                                        <Button size="sm" asChild>
                                            <Link href={route('admin.service-providers.webhooks.create', serviceProvider.id)}>
                                                <Plus className="mr-2 h-4 w-4" />
                                                Első webhook létrehozása
                                            </Link>
                                        </Button>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Oldalsáv */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Állapot
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <label className="text-sm font-medium text-gray-500">Jelenlegi állapot</label>
                                    <div className="mt-1">
                                        <Badge variant={serviceProvider.is_active ? 'default' : 'secondary'}>
                                            {serviceProvider.is_active ? 'Aktív' : 'Inaktív'}
                                        </Badge>
                                    </div>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Webhookok száma</label>
                                    <p className="text-lg font-medium">{serviceProvider.webhooks.length}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Aktív webhookok</label>
                                    <p className="text-lg font-medium">{serviceProvider.webhooks.filter((w) => w.is_active).length}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Létrehozva</label>
                                    <p className="text-sm">{new Date(serviceProvider.created_at).toLocaleString('hu-HU')}</p>
                                </div>

                                <div>
                                    <label className="text-sm font-medium text-gray-500">Utoljára módosítva</label>
                                    <p className="text-sm">{new Date(serviceProvider.updated_at).toLocaleString('hu-HU')}</p>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
