export type PermissionCategory = {
    id: string;
    name: string;
    description: string;
    icon: string;
};

export type Permission = {
    id: number;
    name: string;
    display_name?: string;
    description?: string;
    category: string;
    is_dangerous: boolean;
    sort_order: number;
};

// Permission kategóriák
export const PERMISSION_CATEGORIES: PermissionCategory[] = [
    {
        id: 'system',
        name: 'Rendszer adminisztráció',
        description: 'Rendszerszintű beállítások és konfigurációk',
        icon: 'Settings'
    },
    {
        id: 'user_management',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kezel<PERSON>',
        description: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ók, szerepkörök és jogosultságok kezelése',
        icon: 'Users'
    },
    {
        id: 'repair_management',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON> kezel<PERSON>',
        description: 'Javítási kérelmek és folyamatok kezelése',
        icon: 'Wrench'
    },
    {
        id: 'data_management',
        name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        description: 'Adatok importálása, exportálása és kezelése',
        icon: 'Database'
    },
    {
        id: 'reporting',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        description: 'Jelentések megtekintése és generálása',
        icon: 'BarChart3'
    },
    {
        id: 'general',
        name: 'Általános',
        description: 'Általános jogosultságok',
        icon: 'Shield'
    }
];

// Helper függvények
export function getCategoryInfo(categoryId: string): PermissionCategory | null {
    return PERMISSION_CATEGORIES.find(c => c.id === categoryId) || null;
}

export function getPermissionsByCategory(permissions: Permission[], categoryId: string): Permission[] {
    return permissions.filter(p => p.category === categoryId).sort((a, b) => a.sort_order - b.sort_order);
}

export function searchPermissions(permissions: Permission[], query: string): Permission[] {
    const lowerQuery = query.toLowerCase();
    return permissions.filter(p => 
        (p.display_name?.toLowerCase().includes(lowerQuery)) ||
        (p.description?.toLowerCase().includes(lowerQuery)) ||
        p.name.toLowerCase().includes(lowerQuery)
    );
}

export function getPermissionDisplayName(permission: Permission): string {
    return permission.display_name || permission.name;
}

export function getPermissionDescription(permission: Permission): string {
    return permission.description || 'Nincs leírás';
}
