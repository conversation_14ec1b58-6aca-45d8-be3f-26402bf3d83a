<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UserApprovalNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected bool $approved;
    protected ?string $rejectionReason;

    /**
     * Create a new notification instance.
     */
    public function __construct(bool $approved, ?string $rejectionReason = null)
    {
        $this->approved = $approved;
        $this->rejectionReason = $rejectionReason;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        if ($this->approved) {
            return $this->approvedMail($notifiable);
        } else {
            return $this->rejectedMail($notifiable);
        }
    }

    /**
     * Jóváhagyott regisztráció email
     */
    private function approvedMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('<PERSON>ztr<PERSON><PERSON><PERSON> jóv<PERSON>hagyva - RepairFlow')
            ->greeting('Kedves ' . $notifiable->name . '!')
            ->line('Ö<PERSON>mel értesítjük, hogy a RepairFlow rendszerben történt regisztrációját jóváhagytuk.')
            ->line('Fiókja mostantól aktív, bejelentkezhet a rendszerbe.')
            ->action('Bejelentkezés', url('/login'))
            ->line('Köszönjük, hogy csatlakozott hozzánk!')
            ->salutation('Üdvözlettel,')
            ->salutation('RepairFlow Csapat');
    }

    /**
     * Elutasított regisztráció email
     */
    private function rejectedMail(object $notifiable): MailMessage
    {
        $mail = (new MailMessage)
            ->subject('Regisztráció elutasítva - RepairFlow')
            ->greeting('Kedves ' . $notifiable->name . '!')
            ->line('Sajnálattal értesítjük, hogy a RepairFlow rendszerben történt regisztrációját nem tudtuk jóváhagyni.');

        if ($this->rejectionReason) {
            $mail->line('**Elutasítás indoka:**')
                 ->line($this->rejectionReason);
        }

        $mail->line('Ha kérdése van, vagy úgy érzi, hogy tévedés történt, kérjük, vegye fel velünk a kapcsolatot.')
             ->line('Köszönjük megértését.')
             ->salutation('Üdvözlettel,')
             ->salutation('RepairFlow Csapat');

        return $mail;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'approved' => $this->approved,
            'rejection_reason' => $this->rejectionReason,
        ];
    }
}
