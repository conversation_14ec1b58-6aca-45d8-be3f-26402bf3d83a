<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class VehicleDataControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
    }

    public function test_manufacturers_endpoint_returns_data()
    {
        // Mock the CarQuery API response
        Http::fake([
            'https://www.carqueryapi.com/api/0.3/?cmd=getMakes' => Http::response([
                'Makes' => [
                    ['make_id' => 'ford', 'make_display' => 'Ford'],
                    ['make_id' => 'toyota', 'make_display' => 'Toyota'],
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/manufacturers');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['value', 'label']
                ]
            ]);
    }

    public function test_models_endpoint_requires_manufacturer()
    {
        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/models');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['manufacturer']);
    }

    public function test_models_endpoint_returns_data_with_valid_manufacturer()
    {
        // Mock the CarQuery API response
        Http::fake([
            'https://www.carqueryapi.com/api/0.3/?cmd=getModels&make=ford' => Http::response([
                'Models' => [
                    ['model_name' => 'Focus'],
                    ['model_name' => 'Fiesta'],
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/models?manufacturer=Ford');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['value', 'label']
                ]
            ]);
    }

    public function test_endpoints_require_authentication()
    {
        $response = $this->get('/api/vehicle-data/manufacturers');
        $response->assertRedirect('/login');

        $response = $this->get('/api/vehicle-data/models');
        $response->assertRedirect('/login');
    }
}
