<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PostalVehiclePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Postai járművek jogosultságok létrehozása
        $permissions = [
            'manage_postal_vehicles' => [
                'display_name' => 'Postai járművek kezelése',
                'description' => 'Postai járművek létrehozása, módosítása és törlése',
                'category' => 'data_management',
                'is_dangerous' => false,
                'sort_order' => 400
            ],
            'import_postal_vehicles' => [
                'display_name' => 'Postai járművek tömeges betöltése',
                'description' => 'Postai járművek Excel fájlból történő tömeges importálása',
                'category' => 'data_management',
                'is_dangerous' => true,
                'sort_order' => 401
            ],
        ];

        foreach ($permissions as $name => $attributes) {
            Permission::firstOrCreate(
                ['name' => $name],
                $attributes
            );
        }

        // SystemAdmin szerepkör frissítése - minden jogosultságot megkap
        $systemAdminRole = Role::where('name', 'SystemAdmin')->first();
        if ($systemAdminRole) {
            $systemAdminRole->givePermissionTo(['manage_postal_vehicles', 'import_postal_vehicles']);
            $this->command->info('SystemAdmin szerepkör frissítve a postai járművek jogosultságokkal.');
        }

        // PostaAdmin szerepkör frissítése - csak az alap kezelési jogosultságot kapja meg
        $postaAdminRole = Role::where('name', 'PostaAdmin')->first();
        if ($postaAdminRole) {
            $postaAdminRole->givePermissionTo('manage_postal_vehicles');
            $this->command->info('PostaAdmin szerepkör frissítve a postai járművek kezelési jogosultsággal.');
        }

        // Műszaki menedzser szerepkör frissítése - mindkét jogosultságot megkapja
        $technicalManagerRole = Role::where('name', 'Műszaki menedzser')->first();
        if ($technicalManagerRole) {
            $technicalManagerRole->givePermissionTo(['manage_postal_vehicles', 'import_postal_vehicles']);
            $this->command->info('Műszaki menedzser szerepkör frissítve a postai járművek jogosultságokkal.');
        }

        // Osztályvezető szerepkör frissítése - mindkét jogosultságot megkapja
        $departmentHeadRole = Role::where('name', 'Osztályvezető')->first();
        if ($departmentHeadRole) {
            $departmentHeadRole->givePermissionTo(['manage_postal_vehicles', 'import_postal_vehicles']);
            $this->command->info('Osztályvezető szerepkör frissítve a postai járművek jogosultságokkal.');
        }

        $this->command->info('Postai járművek jogosultságok sikeresen létrehozva és hozzárendelve.');
    }
}
