<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\JsonResponse;

class VehicleDataController extends Controller
{
    private const CARQUERY_BASE_URL = 'https://www.carqueryapi.com/api/0.3';
    private const CACHE_TTL = 3600; // 1 hour cache

    /**
     * Get all vehicle manufacturers from CarQuery API
     */
    public function getManufacturers(): JsonResponse
    {
        //Cache::forget('vehicle_manufacturers');
        try {
            $manufacturers = Cache::remember('vehicle_manufacturers', self::CACHE_TTL, function () {
                $response = Http::timeout(30)->get(self::CARQUERY_BASE_URL, [
                    'cmd' => 'getMakes'
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch manufacturers from CarQuery API');
                }

                $data = $response->json();

                if (!isset($data['Makes']) || !is_array($data['Makes'])) {
                    throw new \Exception('Invalid response format from CarQuery API');
                }

                // Transform the data to match our frontend expectations
                return collect($data['Makes'])
                    ->map(function ($make) {
                        return [
                            'id' => $make['make_id'],
                            'name' => $make['make_display'],
                            'value' => $make['make_display'], // For SingleSelect component
                            'label' => $make['make_display']  // For SingleSelect component
                        ];
                    })
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            });

            return response()->json([
                'success' => true,
                'data' => $manufacturers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch manufacturers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get vehicle models for a specific manufacturer from CarQuery API
     */
    public function getModels(Request $request): JsonResponse
    {
        $request->validate([
            'manufacturer' => 'required|string|max:255'
        ]);

        $manufacturer = $request->input('manufacturer');

        try {
            $cacheKey = 'vehicle_models_' . md5(strtolower($manufacturer));

            $models = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($manufacturer) {
                $response = Http::timeout(30)->get(self::CARQUERY_BASE_URL, [
                    'cmd' => 'getModels',
                    'make' => strtolower($manufacturer)
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch models from CarQuery API');
                }

                $data = $response->json();

                if (!isset($data['Models']) || !is_array($data['Models'])) {
                    throw new \Exception('Invalid response format from CarQuery API');
                }

                // Transform the data to match our frontend expectations
                return collect($data['Models'])
                    ->map(function ($model) {
                        return [
                            'id' => $model['model_name'],
                            'name' => $model['model_name'],
                            'value' => $model['model_name'], // For SingleSelect component
                            'label' => $model['model_name']  // For SingleSelect component
                        ];
                    })
                    ->unique('name') // Remove duplicates
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            });

            return response()->json([
                'success' => true,
                'data' => $models
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch models: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear vehicle data cache (for admin use)
     */
    public function clearCache(): JsonResponse
    {
        try {
            Cache::forget('vehicle_manufacturers');
            
            // Clear all model caches (this is a bit brute force, but effective)
            $cacheKeys = Cache::getRedis()->keys('*vehicle_models_*');
            foreach ($cacheKeys as $key) {
                Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
            }

            return response()->json([
                'success' => true,
                'message' => 'Vehicle data cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }
}
