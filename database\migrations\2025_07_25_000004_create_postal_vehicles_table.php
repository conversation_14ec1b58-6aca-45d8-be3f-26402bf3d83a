<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('postal_vehicles', function (Blueprint $table) {
            $table->id();
            $table->string('license_plate')->unique(); // Rendszám
            $table->string('type'); // Típus (pl. Ford Focus)
            $table->string('chassis_number')->unique(); // Alvázszám
            $table->enum('category', ['N1', 'N2', 'N3']); // Kategória besorolás
            $table->string('contract_classification'); // Szerződés alá történő besorolása
            $table->year('manufacturing_year'); // Gyártási év
            $table->string('fuel_type'); // Üzemanyag
            $table->date('authority_inspection_valid_until'); // Hatósági vizsga érvényessége
            
            // Foreign key kapcsolatok
            $table->foreignId('garage_id')->constrained('garages')->onDelete('cascade'); // Területi illetékes garázs
            $table->string('garage_central_email'); // Területi garázs központi email címe
            $table->foreignId('service_provider_id')->constrained('service_providers')->onDelete('cascade'); // Területileg javításra kijelölt illetékes PF műhely
            
            // Járműmenedzsment munkatárs adatok
            $table->string('technical_staff_name'); // Járműhöz rendelt Járműmenedzsment műszaki munkatárs
            $table->string('technical_staff_email'); // Járműmenedzsment műszaki munkatárs email címe
            $table->string('technical_manager_name'); // Járműmenedzsment műszaki menedzser neve
            $table->string('technical_manager_email'); // Járműmenedzsment műszaki menedzser email címe
            
            $table->timestamps();
            
            // Indexek
            $table->index(['license_plate']);
            $table->index(['type']);
            $table->index(['category']);
            $table->index(['garage_id']);
            $table->index(['service_provider_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('postal_vehicles');
    }
};
