<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('postal_vehicles', function (Blueprint $table) {
            // Add new manufacturer and model fields
            $table->string('manufacturer')->nullable()->after('license_plate');
            $table->string('model')->nullable()->after('manufacturer');
            
            // Keep the old type field for now to allow for data migration
            // We'll remove it in a separate migration after data is migrated
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('postal_vehicles', function (Blueprint $table) {
            $table->dropColumn(['manufacturer', 'model']);
        });
    }
};
