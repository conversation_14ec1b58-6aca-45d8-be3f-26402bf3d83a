<?php

namespace App\Rules;

use App\Models\ServiceProvider;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ActiveServiceProvider implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        // A 'value' itt a 'szerviz_neve' oszlop értéke lesz.
        $serviceProvider = ServiceProvider::where('name', $value)->first();

        // Az 'exists' s<PERSON><PERSON><PERSON><PERSON>, ha nem létezik,
        // de a biztonság kedvéért itt is ellenőrizzük.
        if (!$serviceProvider) {
            return;
        }

        if (!$serviceProvider->is_active) {
            // A $fail callback segítségével adunk hozzá hibaüzenetet.
            $fail('A(z) "' . $value . '" nevű szerviz nem aktív, nem rendel<PERSON>t<PERSON> hozz<PERSON> j<PERSON>.');
        }
    }
}