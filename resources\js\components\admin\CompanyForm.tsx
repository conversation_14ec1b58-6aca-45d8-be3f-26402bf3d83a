import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useForm } from '@inertiajs/react';
import { Building2, LocateFixed, Mail, Map, MapPin, Phone } from 'lucide-react';
import { FormEvent } from 'react';

interface CompanyFormProps {
    company: {
        id: number;
        name: string;
        zip?: string;
        city?: string;
        address?: string;
        phone?: string;
        email?: string;
    } | null;
    onClose: () => void;
}

export default function CompanyForm({ company, onClose }: CompanyFormProps) {
    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: company?.name ?? '',
        zip: company?.zip ?? '',
        city: company?.city ?? '',
        address: company?.address ?? '',
        phone: company?.phone ?? '',
        email: company?.email ?? '',
    });

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();

        const method = company ? put : post;
        const url = company ? route('companies.update', company.id) : route('companies.store');

        method(url, {
            onSuccess: () => {
                reset();
                onClose();
            },
        });
    };

    return (
        <form onSubmit={handleSubmit} className="flex h-full flex-col">
            <div className="space-y-4 py-4">
                <div className="grid gap-2">
                    <Label htmlFor="name">Név</Label>
                    <div className="relative">
                        <Building2 className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                        <Input id="name" tabIndex={1} value={data.name} onChange={(e) => setData('name', e.target.value)} className="pl-10" />
                        {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                    </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                    <div className="grid gap-2">
                        <Label htmlFor="zip">Irányítószám</Label>
                        <div className="relative">
                            <LocateFixed className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input id="zip" tabIndex={2} value={data.zip} onChange={(e) => setData('zip', e.target.value)} className="pl-10" />
                        </div>
                    </div>
                    <div className="col-span-2 grid gap-2">
                        <Label htmlFor="city">Város</Label>
                        <div className="relative">
                            <Map className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input id="city" tabIndex={3} value={data.city} onChange={(e) => setData('city', e.target.value)} className="pl-10" />
                        </div>
                    </div>
                </div>
                <div className="grid gap-2">
                    <Label htmlFor="address">Cím</Label>
                    <div className="relative">
                        <MapPin className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                        <Input
                            id="address"
                            tabIndex={4}
                            value={data.address}
                            onChange={(e) => setData('address', e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="phone">Telefonszám</Label>
                    <div className="relative">
                        <Phone className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                        <Input
                            id="phone"
                            tabIndex={5}
                            type="tel"
                            value={data.phone}
                            onChange={(e) => setData('phone', e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>

                <div className="grid gap-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="relative">
                        <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                        <Input
                            id="email"
                            tabIndex={6}
                            type="email"
                            value={data.email}
                            onChange={(e) => setData('email', e.target.value)}
                            className="pl-10"
                        />
                    </div>
                </div>
            </div>

            <DialogFooter className="px-0">
                <Button type="button" variant="outline" onClick={onClose}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {company ? 'Mentés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </form>
    );
}
