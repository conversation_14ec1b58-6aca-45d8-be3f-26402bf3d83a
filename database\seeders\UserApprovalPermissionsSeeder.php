<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class UserApprovalPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Új jogosultságok létrehozása
        $permissions = [
            'manage_users' => 'Partner felhasználók jóváhagyása',
            'manage_posta_users' => 'Posta felhasználók jóváhagyása',
        ];

        foreach ($permissions as $name => $description) {
            Permission::firstOrCreate(
                ['name' => $name],
                ['description' => $description]
            );
        }

        // SystemAdmin szerepkör megkeresése és jogosultságok hozzáadása
        $systemAdminRole = Role::where('name', 'SystemAdmin')->first();
        
        if ($systemAdminRole) {
            // SystemAdmin mindkét jogosultságot megkapja
            $systemAdminRole->givePermissionTo(['manage_users', 'manage_posta_users']);
            $this->command->info('SystemAdmin szerepkör frissítve a felhasználó jóváhagyási jogosultságokkal.');
        }

        // PostaAdmin szerepkör megkeresése (ha létezik)
        $postaAdminRole = Role::where('name', 'PostaAdmin')->first();
        
        if ($postaAdminRole) {
            // PostaAdmin csak a Posta felhasználók jóváhagyását kapja meg
            $postaAdminRole->givePermissionTo('manage_posta_users');
            $this->command->info('PostaAdmin szerepkör frissítve a Posta felhasználó jóváhagyási jogosultsággal.');
        } else {
            // Ha nincs PostaAdmin szerepkör, létrehozzuk
            $postaAdminRole = Role::create([
                'name' => 'PostaAdmin',
                'description' => 'Magyar Posta adminisztrátor'
            ]);
            $postaAdminRole->givePermissionTo('manage_posta_users');
            $this->command->info('PostaAdmin szerepkör létrehozva a Posta felhasználó jóváhagyási jogosultsággal.');
        }

        $this->command->info('Felhasználó jóváhagyási jogosultságok sikeresen létrehozva és hozzárendelve.');
    }
}
