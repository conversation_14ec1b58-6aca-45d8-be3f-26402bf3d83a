import { ContactForm } from '@/components/admin/ContactForm';
import { ImportDropzone } from '@/components/shared/ImportDropzone';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import AppLayout from '@/layouts/app-layout';
import { csrfFetch } from '@/lib/utils';
import { type BreadcrumbItem } from '@/types';
import { PageProps } from '@inertiajs/core';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { AlertCircle, Download, Filter, Plus, Search, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';

type Contact = {
    id: number;
    name: string;
    email: string;
    username: string;
    org_unit_id?: number;
    garage_id?: number;
    org_unit?: { id: number; name: string };
    garage?: { id: number; name: string; postal_code: string; city: string; address: string };
};

type OrgUnit = { id: number; name: string };
type Garage = { id: number; name: string; postal_code: string; city: string; address: string };

interface Props extends PageProps {
    contacts: Contact[];
    orgUnits: OrgUnit[];
    garages: Garage[];
    filters: { search?: string; org_unit?: string; garage?: string };
    auth: {
        user: {
            permissions: string[];
        };
    };
    flash: {
        import_errors?: string[];
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Kapcsolattartók', href: '/admin/contacts' }];

export default function ContactsIndex({ contacts, orgUnits, garages, filters, auth }: Props) {
    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<Contact | null>(null);
    const [deleteTarget, setDeleteTarget] = useState<Contact | null>(null);
    const [globalFilter, setGlobalFilter] = useState(filters.search || '');
    const [orgUnitFilter, setOrgUnitFilter] = useState(filters.org_unit || 'all');
    const [garageFilter, setGarageFilter] = useState(filters.garage || 'all');

    // Import states
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const { flash = {} } = usePage<Props>().props;
    const importErrors = flash.import_errors || [];

    const canImport = auth.user.permissions.includes('import_postal_contacts');

    useEffect(() => {
        if (importErrors.length > 0) {
            setIsImportModalOpen(true);
        }
    }, [importErrors]);

    const handleEdit = (contact: Contact) => {
        setEditing(contact);
        setOpen(true);
    };
    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };
    const confirmDelete = (contact: Contact) => {
        setDeleteTarget(contact);
    };

    const performDelete = async () => {
        if (deleteTarget) {
            await router.delete(`/admin/contacts/${deleteTarget.id}`);
            setDeleteTarget(null);
        }
    };

    const handleSearch = () => {
        router.get(
            '/admin/contacts',
            {
                search: globalFilter,
                org_unit: orgUnitFilter === 'all' ? '' : orgUnitFilter,
                garage: garageFilter === 'all' ? '' : garageFilter,
            },
            { preserveState: true, replace: true },
        );
    };

    const clearFilters = () => {
        setGlobalFilter('');
        setOrgUnitFilter('all');
        setGarageFilter('all');
        router.get('/admin/contacts', {}, { preserveState: true, replace: true });
    };

    const handleExportExcel = () => {
        window.location.href = '/admin/contacts/export';
    };

    const handleCreateOrgUnit = async (name: string): Promise<OrgUnit> => {
        const response = await csrfFetch('/admin/org-units', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name }),
        });

        if (!response.ok) {
            throw new Error('Nem sikerült létrehozni a szervezeti egységet.');
        }

        const newOrgUnit: OrgUnit = await response.json();

        return newOrgUnit; // A SingleSelect ezt kiválasztja
    };

    const handleCreateGarage = async (name: string): Promise<Garage> => {
        const response = await csrfFetch('/admin/garages', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name,
                postal_code: '0000', // Default values - user can edit later
                city: 'Ismeretlen',
                address: 'Ismeretlen',
            }),
        });

        if (!response.ok) {
            throw new Error('Failed to create garage');
        }

        const newGarage = await response.json();
        router.reload({ only: ['garages'] }); // Refresh garages list
        return newGarage;
    };

    const columns: ColumnDef<Contact>[] = [
        { header: 'Név', accessorKey: 'name', meta: { align: 'left', sortable: true } },
        { header: 'Email', accessorKey: 'email', meta: { align: 'left', sortable: true } },
        { header: 'Felhasználónév', accessorKey: 'username', meta: { align: 'left', sortable: true } },
        {
            header: 'Szervezeti egység',
            accessorKey: 'org_unit.name',
            cell: ({ row }) => row.original.org_unit?.name || '-',
            meta: { align: 'left', sortable: false },
        },
        {
            header: 'Garázs',
            accessorKey: 'garage.name',
            cell: ({ row }) => row.original.garage?.name || '-',
            meta: { align: 'left', sortable: false },
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Kapcsolattartók" />
            <div className="p-4">
                <div className="flex items-center justify-between space-y-4 pt-3">
                    <Button variant="outline" onClick={handleExportExcel}>
                        <Download className="mr-2 h-4 w-4" />
                        Export
                    </Button>
                    {canImport && (
                        <Dialog open={isImportModalOpen} onOpenChange={setIsImportModalOpen}>
                            <DialogTrigger asChild>
                                <Button variant="outline">
                                    <Upload className="mr-2 h-4 w-4" /> Import
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                    <DialogTitle>Kapcsolattartók importálása</DialogTitle>
                                    <DialogDescription>
                                        Töltsön fel egy CSV vagy Excel fájlt a kapcsolattartók tömeges hozzáadásához.
                                    </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                    {importErrors.length > 0 && (
                                        <Alert variant="destructive">
                                            <AlertCircle className="h-4 w-4" />
                                            <AlertTitle>Importálási hibák</AlertTitle>
                                            <AlertDescription>
                                                <ul className="mt-2 list-disc space-y-1 pl-5">
                                                    {importErrors.map((error, index) => (
                                                        <li key={index}>{error}</li>
                                                    ))}
                                                </ul>
                                            </AlertDescription>
                                        </Alert>
                                    )}
                                    <ImportDropzone
                                        uploadRouteName="admin.contacts.import"
                                        onDownloadSample={() => (window.location.href = route('admin.contacts.download-sample'))}
                                        onUploadSuccess={() => setIsImportModalOpen(false)}
                                    />
                                </div>
                            </DialogContent>
                        </Dialog>
                    )}
                    <div className="flex items-center gap-2">
                        <Button onClick={handleAdd}>
                            <Plus className="mr-2 h-4 w-4" />
                            Új kapcsolattartó
                        </Button>
                    </div>
                </div>

                <div className="space-y-4 rounded-lg border bg-white p-4">
                    <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
                        <div>
                            <Label>Szervezeti egység</Label>
                            <Select value={orgUnitFilter} onValueChange={setOrgUnitFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Összes" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Összes</SelectItem>
                                    {orgUnits.map((unit) => (
                                        <SelectItem key={unit.id} value={unit.id.toString()}>
                                            {unit.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div>
                            <Label>Garázs</Label>
                            <Select value={garageFilter} onValueChange={setGarageFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Összes" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">Összes</SelectItem>
                                    {garages.map((garage) => (
                                        <SelectItem key={garage.id} value={garage.id.toString()}>
                                            {garage.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="flex items-end gap-2">
                            <Button onClick={handleSearch}>
                                <Filter className="mr-2 h-4 w-4" />
                                Szűrés
                            </Button>
                            <Button variant="outline" onClick={clearFilters}>
                                Törlés
                            </Button>
                        </div>
                        <div>
                            <Label htmlFor="search">&nbsp;</Label>
                            <div className="relative">
                                <Search className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search"
                                    placeholder="Név, email, felhasználónév..."
                                    value={globalFilter}
                                    onChange={(e) => setGlobalFilter(e.target.value)}
                                    className="pl-10"
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div className="mt-2">
                    <DataTable columns={columns} data={contacts} pageSize={10} onEdit={handleEdit} onDelete={confirmDelete} />
                </div>
            </div>

            <Dialog
                open={open}
                onOpenChange={(isOpen) => {
                    // Csak akkor zárjuk be, ha programmatikusan hívjuk
                    if (!isOpen) {
                        setOpen(false);
                    }
                }}
            >
                <DialogTitle></DialogTitle>
                <DialogContent
                    className="flex max-h-[90vh] max-w-2xl flex-col p-0"
                    onPointerDownOutside={(e) => {
                        // Megakadályozzuk a bezáródást mellé kattintásra
                        e.preventDefault();
                    }}
                    onEscapeKeyDown={() => {
                        // ESC billentyűre is bezárjuk
                        setOpen(false);
                    }}
                >
                    <div className="p-6 pb-0">
                        <h2 className="text-lg font-semibold">{editing ? 'Kapcsolattartó szerkesztése' : 'Új kapcsolattartó'}</h2>
                        <p className="text-sm text-muted-foreground">Töltsd ki az alábbi űrlapot a kapcsolattartó adatainak mentéséhez.</p>
                    </div>

                    <div className="flex-1 overflow-hidden">
                        <ContactForm
                            contact={editing}
                            orgUnits={orgUnits}
                            garages={garages}
                            onClose={() => setOpen(false)}
                            onCreateOrgUnit={handleCreateOrgUnit}
                            onCreateGarage={handleCreateGarage}
                        />
                    </div>
                </DialogContent>
            </Dialog>

            <AlertDialog open={!!deleteTarget} onOpenChange={() => setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Biztosan törölni szeretné ezt a kapcsolattartót?</AlertDialogTitle>
                        <p>
                            A művelet nem vonható vissza. A(z) <strong>{deleteTarget?.name}</strong> nevű kapcsolattartó véglegesen törlődni fog.
                        </p>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégsem</AlertDialogCancel>
                        <AlertDialogAction onClick={performDelete} className="bg-red-600 text-white hover:bg-red-700">
                            Törlés
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
