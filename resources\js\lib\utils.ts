import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
    return twMerge(clsx(inputs));
}

export const csrfFetch = (url: string, options: RequestInit = {}) => {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';

    const headers = {
        'X-CSRF-TOKEN': token,
        'X-Requested-With': 'XMLHttpRequest',
        ...(options.headers || {}),
    };

    return fetch(url, {
        ...options,
        headers,
    });
};
