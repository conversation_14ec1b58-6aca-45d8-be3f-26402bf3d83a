import AppLogoPng from '@/components/app-logo-png';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { Link } from '@inertiajs/react';
import { type PropsWithChildren } from 'react';

export default function AuthCardLayout({
    children,
    title,
    description,
    two_column,
}: PropsWithChildren<{
    name?: string;
    title?: string;
    description?: string;
    two_column?: boolean;
}>) {
    return (
        <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
            <div className={cn('flex w-full flex-col', two_column ? 'max-w-4xl' : 'max-w-md')}>
                <div className="flex flex-col gap-6">
                    <Card className="rounded-xl">
                        <div className="flex w-full flex-col gap-6">
                            <Link href={route('home')} className="flex items-center gap-2 self-center font-medium">
                                <div className="flex h-19 w-30 items-center justify-center">
                                    <AppLogoPng className="" />
                                </div>
                            </Link>
                        </div>
                        <CardHeader className="px-10 pt-3 pb-0 text-center">
                            <CardTitle className="text-xl">{title}</CardTitle>
                            <CardDescription>{description}</CardDescription>
                        </CardHeader>
                        <CardContent className="px-10 py-8">{children}</CardContent>
                    </Card>
                </div>
            </div>
        </div>
    );
}
