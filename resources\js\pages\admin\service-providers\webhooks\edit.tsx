import WebhookForm from '@/components/admin/WebhookForm';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, router } from '@inertiajs/react';
import { ArrowLeft, Building2, Webhook } from 'lucide-react';

interface ServiceProvider {
    id: number;
    name: string;
    city: string;
}

interface Webhook {
    id: number;
    name: string;
    event_type: string;
    url: string;
    method: string;
    headers?: Record<string, string>;
    payload_template?: Record<string, any>;
    secret?: string;
    timeout: number;
    retry_attempts: number;
    is_active: boolean;
}

interface Props {
    serviceProvider: ServiceProvider;
    webhook: Webhook;
    eventTypes: Record<string, string>;
    httpMethods: string[];
}

export default function EditWebhook({ serviceProvider, webhook, eventTypes, httpMethods }: Props) {
    const handleCancel = () => {
        router.visit(route('admin.service-providers.webhooks.index', serviceProvider.id));
    };

    return (
        <AppLayout>
            <Head title={`${webhook.name} szerkesztése - ${serviceProvider.name}`} />

            <div className="space-y-6">
                <div className="flex items-center gap-4">
                    <Link
                        href={route('admin.service-providers.webhooks.index', serviceProvider.id)}
                        className="flex h-10 w-10 items-center justify-center rounded-full border bg-white text-gray-600 transition-colors hover:bg-gray-100"
                    >
                        <ArrowLeft className="h-5 w-5" />
                    </Link>
                    <div>
                        <h1 className="flex items-center gap-2 text-2xl font-bold text-gray-900">
                            <Webhook className="h-6 w-6 text-blue-600" />
                            Webhook szerkesztése
                        </h1>
                        <p className="flex items-center gap-2 text-gray-600">
                            <Building2 className="h-4 w-4" />
                            {serviceProvider.name}
                        </p>
                    </div>
                </div>
                <div className="mx-auto max-w-6xl rounded-lg border bg-white p-6 shadow-sm">
                    <WebhookForm serviceProvider={serviceProvider} webhook={webhook} httpMethods={httpMethods} onCancel={handleCancel} />
                </div>
            </div>
        </AppLayout>
    );
}
