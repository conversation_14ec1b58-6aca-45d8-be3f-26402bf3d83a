import CompanyForm from '@/components/admin/CompanyForm';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Download, Search } from 'lucide-react';
import { useState } from 'react';
import * as XLSX from 'xlsx';

interface Company {
    id: number;
    name: string;
    zip?: string;
    city?: string;
    address?: string;
    phone?: string;
    email?: string;
}

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Cégek', href: '/companies' }];

export default function CompanyIndex() {
    const { companies } = usePage<{ companies: Company[] }>().props;

    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<Company | null>(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [deleteTarget, setDeleteTarget] = useState<Company | null>(null);

    const columns: ColumnDef<Company>[] = [
        { header: 'Név', accessorKey: 'name', meta: { align: 'left', sortable: true } },
        { header: 'Irányítószám', accessorKey: 'zip', meta: { align: 'left', sortable: false } },
        { header: 'Város', accessorKey: 'city', meta: { align: 'left', sortable: false } },
        { header: 'Cím', accessorKey: 'address', meta: { align: 'left', sortable: false } },
        { header: 'Telefon', accessorKey: 'phone', meta: { align: 'left', sortable: false } },
        { header: 'Email', accessorKey: 'email', meta: { align: 'left', sortable: false } },
    ];

    const handleEdit = (company: Company) => {
        setEditing(company);
        setOpen(true);
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const confirmDelete = (company: Company) => {
        setDeleteTarget(company);
    };

    const performDelete = async () => {
        if (deleteTarget) {
            await router.delete(route('companies.destroy', deleteTarget.id));
            setDeleteTarget(null);
        }
    };

    const handleExportExcel = (data: Company[]) => {
        const exportData = data.map((c) => ({
            Név: c.name,
            Irányítószám: c.zip ?? '',
            Város: c.city ?? '',
            Cím: c.address ?? '',
            Telefon: c.phone ?? '',
            Email: c.email ?? '',
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Cégek');
        XLSX.writeFile(workbook, 'cegek.xlsx');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Cégek" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {' '}
                <div className="flex items-center justify-between">
                    <Button onClick={() => handleExportExcel(companies)} variant="outline" size="sm" className="flex items-center gap-2">
                        <Download className="h-4 w-4" /> Excel export
                    </Button>
                    <div className="flex items-center gap-3">
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Keresés..."
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                className="w-64 border-gray-200 pl-10 shadow-sm focus:border-blue-500 focus:ring-blue-500/20"
                            />
                        </div>
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                    + Új cég
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="flex max-h-[90vh] max-w-xl flex-col p-0"
                                onInteractOutside={(e) => {
                                    // Megakadályozzuk, hogy a dialog bezáródjon háttérkattintásra
                                    e.preventDefault();
                                }}
                            >
                                <DialogHeader className="p-6 pb-0">
                                    <DialogTitle>{editing ? 'Cég szerkesztése' : 'Új cég létrehozása'}</DialogTitle>
                                </DialogHeader>

                                <div className="flex-1 overflow-y-auto px-6">
                                    <CompanyForm company={editing} onClose={() => setOpen(false)} />
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
                <DataTable
                    columns={columns}
                    data={companies}
                    onEdit={handleEdit}
                    onDelete={confirmDelete}
                    globalFilter={globalFilter}
                    exportFileName="cegek"
                    pageSize={10}
                    showExportButton={false}
                />
            </div>

            <AlertDialog open={!!deleteTarget} onOpenChange={(open) => !open && setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Biztosan törölni szeretnéd ezt a céget?</AlertDialogTitle>
                        <p className="text-muted-foreground">{deleteTarget?.name}</p>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégsem</AlertDialogCancel>
                        <AlertDialogAction onClick={performDelete} className="bg-red-600 text-white hover:bg-red-700">
                            Törlés
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
