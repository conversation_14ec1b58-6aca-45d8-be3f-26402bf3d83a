<?php

namespace App\Imports;

use App\Models\Garage;
use App\Models\PostalVehicle;
use App\Models\ServiceProvider;
use App\Rules\ActiveServiceProvider;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class PostalVehicleImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    private $garages;
    private $serviceProviders;

    public function __construct()
    {
        // Gyorsítótárazzuk a szükséges adatokat, hogy ne kelljen minden sornál lekérdezni.
        $this->garages = Garage::all()->keyBy('name');
        $this->serviceProviders = ServiceProvider::all()->keyBy('name');
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new PostalVehicle([
            'license_plate' => $row['rendszam'],
            'manufacturer' => $row['gyartmany'] ?? $row['tipus'] ?? '', // Support both new and old format
            'model' => $row['modell'] ?? '',
            'type' => $row['tipus'] ?? null, // Keep for backward compatibility
            'chassis_number' => $row['alvazsszam'],
            'category' => $row['kategoria'],
            'contract_classification' => $row['szerzodes_besorolas'],
            'manufacturing_year' => $row['gyartasi_ev'],
            'fuel_type' => $row['uzemanyag'],
            'authority_inspection_valid_until' => $row['hatosagi_vizsga_ervenyessege'],
            'garage_id' => $this->garages->get($row['garazs_neve'])->id,
            'garage_central_email' => $row['garazs_email'],
            'service_provider_id' => $this->serviceProviders->get($row['szerviz_neve'])->id,
            'technical_staff_name' => $row['muszaki_munkatars_neve'],
            'technical_staff_email' => $row['muszaki_munkatars_email'],
            'technical_manager_name' => $row['muszaki_menedzser_neve'],
            'technical_manager_email' => $row['muszaki_menedzser_email'],
        ]);
    }

    public function rules(): array
    {
        return [
            'rendszam' => 'required|string|max:20|unique:postal_vehicles,license_plate',
            'gyartmany' => 'required_without:tipus|string|max:255',
            'modell' => 'required_with:gyartmany|string|max:255',
            'tipus' => 'required_without:gyartmany|string|max:255', // Keep for backward compatibility
            'alvazsszam' => 'required|string|max:255|unique:postal_vehicles,chassis_number',
            'kategoria' => 'required|in:N1,N2,N3',
            'gyartasi_ev' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'hatosagi_vizsga_ervenyessege' => 'required|date_format:Y-m-d|after:today',

            // Ellenőrizzük, hogy a garázs és szerviz létezik-e a DB-ben
            'garazs_neve' => 'required|exists:garages,name',
            'szerviz_neve' => ['required', 'exists:service_providers,name', new ActiveServiceProvider],

            'muszaki_munkatars_email' => 'required|email',
            'muszaki_menedzser_email' => 'required|email',
        ];
    }

    /**
     * Egyéni validációs logika hozzáadása.
     * Ez a validátor lefutása *után* hívódik meg.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Itt a $validator->getData() a teljes beolvasott kollekciót adja vissza,
            // de a hibaüzeneteket soronként kell kezelnünk.
            // A Laravel Excel ezt kezeli nekünk, ha a `ToCollection` helyett `ToModel`-t használunk.
            // A példa kedvéért: ellenőrizzük, hogy a garázs neve és email címe összetartozik-e.
            
            $row = $validator->getData();
            $garage = $this->garages->get($row['garazs_neve'] ?? null);

            if ($garage && $garage->central_email !== ($row['garazs_email'] ?? null)) {
                $validator->errors()->add('garazs_email', 'A megadott garázs email cím nem egyezik a "' . $row['garazs_neve'] . '" garázshoz rendelt címmel!');
            }
        });
    }

    public function batchSize(): int
    {
        return 100; // Hány sort dolgozzon fel egy adatbázis tranzakcióban
    }
}