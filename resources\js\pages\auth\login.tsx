import { Head, useForm } from '@inertiajs/react';
import { LoaderCircle } from 'lucide-react';
import { FormEventHandler } from 'react';

import PasswordInput from '@/components/form/password-input';
import InputError from '@/components/input-error';
import TextLink from '@/components/text-link';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import useTranslations from '@/hooks/use-translations';
import AuthLayout from '@/layouts/auth-layout';
import { Mail } from 'lucide-react';
import { useState } from 'react';

type LoginForm = {
    email: string;
    password: string;
    remember: boolean;
};

interface LoginProps {
    status?: string;
    canResetPassword: boolean;
}

export default function Login({ status, canResetPassword }: LoginProps) {
    const { data, setData, post, processing, errors, reset } = useForm<Required<LoginForm>>({
        email: '',
        password: '',
        remember: false,
    });

    const submit: FormEventHandler = (e) => {
        e.preventDefault();
        post(route('login'), {
            onFinish: () => reset('password'),
        });
    };

    const { t } = useTranslations('auth');

    const [showPassword, setShowPassword] = useState(false);

    return (
        <AuthLayout title={t('login_title')} description={t('login_description')}>
            <Head title={t('login')} />

            <form className="flex flex-col gap-6" onSubmit={submit}>
                <div className="grid gap-6">
                    <div className="grid gap-2">
                        <Label htmlFor="email">{t('email')}</Label>
                        <div className="relative">
                            <Mail className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                            <Input
                                id="email"
                                type="email"
                                required
                                autoFocus
                                tabIndex={1}
                                autoComplete="email"
                                value={data.email}
                                onChange={(e) => setData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                className="pl-10"
                            />
                        </div>
                        <InputError message={errors.email} />
                    </div>

                    <PasswordInput
                        id="password"
                        name="password"
                        label={t('password')}
                        value={data.password}
                        onChange={(e) => setData('password', e.target.value)}
                        error={errors.password}
                        placeholder={t('password')}
                        autoComplete="current-password"
                        required
                        tabIndex={2}
                        actionSlot={
                            canResetPassword ? (
                                <TextLink href={route('password.request')} tabIndex={5}>
                                    {t('forgot_password')}?
                                </TextLink>
                            ) : null
                        }
                    />

                    <div className="flex items-center space-x-3">
                        <Checkbox
                            id="remember"
                            name="remember"
                            checked={data.remember}
                            onClick={() => setData('remember', !data.remember)}
                            tabIndex={3}
                        />
                        <Label htmlFor="remember">{t('remember_me')}</Label>
                    </div>

                    <Button type="submit" className="mt-4 w-full" tabIndex={4} disabled={processing}>
                        {processing && <LoaderCircle className="h-4 w-4 animate-spin" />}
                        {t('login')}
                    </Button>
                </div>

                <div className="text-center text-sm text-muted-foreground">
                    {t('dont_have_account')}{' '}
                    <TextLink href={route('register')} tabIndex={5}>
                        {t('sign_up')}
                    </TextLink>
                </div>
            </form>

            {status && <div className="mb-4 text-center text-sm font-medium text-green-600">{status}</div>}
        </AuthLayout>
    );
}
