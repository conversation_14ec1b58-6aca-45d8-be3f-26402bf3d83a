<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Inertia\Inertia;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Inertia::share([
            'translations' => function () {
                $locale = App::getLocale();
                $translationPath = base_path("lang/{$locale}");

                if (!File::isDirectory($translationPath)) {
                    return [];
                }

                return collect(File::allFiles($translationPath))->flatMap(function ($file) use ($locale) {
                    $filename = $file->getFilenameWithoutExtension();
                    return [$filename => trans($filename)];
                });
            },
            'locale' => fn () => App::getLocale(),
        ]);
    }
}
