<?php

namespace App\Imports;

use App\Models\Contact;
use App\Models\Garage;
use App\Models\OrgUnit;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\WithBatchInserts;

class ContactImport implements ToModel, WithHeadingRow, WithValidation, WithBatchInserts
{
    private $orgUnits;
    private $garages;

    public function __construct()
    {
        // Gyorsítótárazzuk a szükséges adatokat, hogy ne kelljen minden sornál lekérdezni.
        $this->orgUnits = OrgUnit::all()->keyBy('name');
        $this->garages = Garage::all()->keyBy('name');
    }

    public function model(array $row)
    {
        $orgUnitName = $row['szervezeti_egyseg'] ?? null;
        $garageName = $row['garazs'] ?? null;

        return new Contact([
            'name'       => $row['nev'],
            'email'      => $row['email'],
            'username'   => $row['felhasznalonev'] ?? null,
            'org_unit_id' => $orgUnitName ? $this->orgUnits->get($orgUnitName)?->id : null,
            'garage_id'  => $garageName ? $this->garages->get($garageName)?->id : null,
        ]);
    }

    public function rules(): array
    {
        return [
            'nev' => 'required|string|max:255',
            'email' => 'required|email|unique:contacts,email',
            'felhasznalonev' => 'nullable|string|max:255',

            // Ellenőrizzük, hogy a szervezeti egység és garázs létezik-e a DB-ben
            'szervezeti_egyseg' => 'nullable|string|exists:org_units,name',
            'garazs' => 'nullable|string|exists:garages,name',
        ];
    }

    public function batchSize(): int
    {
        return 100; // Hány sort dolgozzon fel egy adatbázis tranzakcióban
    }
}