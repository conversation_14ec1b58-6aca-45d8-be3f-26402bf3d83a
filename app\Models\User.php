<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Collection;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'company_type',
        'company_name',
        'tax_number',
        'position',
        'phone',
        'is_active',
        'contact_id',
        'org_unit_id',
        'garage_id',
        'approved_at',
        'approved_by',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
            'approved_at' => 'datetime',
        ];
    }

    public function companies()
    {
        return $this->belongsToMany(Company::class);
    }

    /**
     * Kapcsolat a contacts táblával (Posta felhasználókhoz)
     */
    public function contact()
    {
        return $this->belongsTo(Contact::class);
    }

    /**
     * Kapcsolat a szervezeti egységgel
     */
    public function orgUnit()
    {
        return $this->belongsTo(OrgUnit::class);
    }

    /**
     * Kapcsolat a garázzsal
     */
    public function garage()
    {
        return $this->belongsTo(Garage::class);
    }

    /**
     * Kapcsolat a jóváhagyó adminnal
     */
    public function approvedBy()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Visszaadja az összes létező, egyedi pozíciót a felhasználók közül,
     * a frontend számára megfelelő formátumban.
     */
    public static function getExistingPositions(): Collection
    {
        return self::query()
            ->select('position')
            ->whereNotNull('position')
            ->where('position', '!=', '')
            ->distinct()
            ->orderBy('position')
            ->get()
            ->map(fn ($user) => ['id' => $user->position, 'name' => $user->position]);
    } 

    /**
     * Scope: csak aktív felhasználók
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope: Posta felhasználók
     */
    public function scopePosta($query)
    {
        return $query->where('company_type', 'posta');
    }

    /**
     * Scope: Partner felhasználók
     */
    public function scopePartner($query)
    {
        return $query->where('company_type', 'partner');
    }
        
}
