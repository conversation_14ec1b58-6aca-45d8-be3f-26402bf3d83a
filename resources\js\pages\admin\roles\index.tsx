import RoleForm from '@/components/admin/RoleForm';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { type Permission } from '@/utils/permissions';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Download, Search } from 'lucide-react';
import { useState } from 'react';
import * as XLSX from 'xlsx';

type Role = {
    id: number;
    name: string;
    approval_limit: number | null;
    permissions: { name: string }[];
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Szerepkörök',
        href: '/roles',
    },
];

export default function RoleIndex() {
    const { roles, permissions } = usePage<{
        roles: Role[];
        permissions: Permission[];
    }>().props;

    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<Role | null>(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [deleteTarget, setDeleteTarget] = useState<Role | null>(null);

    const columns: ColumnDef<Role>[] = [
        {
            header: 'Név',
            accessorKey: 'name',
            meta: { align: 'left', sortable: true },
        },
        {
            header: 'Összeghatár',
            accessorKey: 'approval_limit',
            meta: { align: 'right', sortable: true },
            cell: ({ row }) => row.original.approval_limit?.toLocaleString() ?? '-',
        },
        {
            header: 'Jogosultságok',
            meta: { align: 'left', sortable: false },
            accessorFn: (row) => row.permissions.map((p) => p.name),
            id: 'permissions',
        },
    ];

    const handleEdit = (role: Role) => {
        setEditing(role);
        setOpen(true);
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const confirmDelete = (role: Role) => {
        setDeleteTarget(role);
    };

    const performDelete = () => {
        if (deleteTarget) {
            router.delete(`/admin/roles/${deleteTarget.id}`);
            setDeleteTarget(null);
        }
    };

    const handleExportExcel = (data: Role[]) => {
        const exportData = data.map((role) => ({
            Név: role.name,
            Összeghatár: role.approval_limit?.toLocaleString() ?? '-',
            Jogosultságok: role.permissions.map((p) => p.name).join(', '),
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Szerepkörök');
        XLSX.writeFile(workbook, 'szerepkorok.xlsx');
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Szerepkörök" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {' '}
                {/* Header with title, search and add button */}
                <div className="flex items-center justify-between">
                    <Button onClick={() => handleExportExcel(roles)} variant="outline" size="sm" className="flex items-center gap-2">
                        <Download className="h-4 w-4" />
                        Excel export
                    </Button>
                    <div className="flex items-center gap-3">
                        {/* Search input */}
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Keresés..."
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                className="w-64 border-gray-200 pl-10 shadow-sm focus:border-blue-500 focus:ring-blue-500/20"
                            />
                        </div>

                        {/* Add button */}
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:cursor-pointer hover:bg-green-600">
                                    + Új szerepkör
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="w-[90vw] max-w-5xl md:max-w-6xl"
                                onInteractOutside={(e) => {
                                    // Megakadályozzuk, hogy a dialog bezáródjon háttérkattintásra
                                    e.preventDefault();
                                }}
                            >
                                <RoleForm role={editing} permissions={permissions} onClose={() => setOpen(false)} />
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
                {/* DataTable with pagination and export */}
                <DataTable
                    columns={columns}
                    data={roles}
                    onEdit={handleEdit}
                    onDelete={confirmDelete}
                    globalFilter={globalFilter}
                    exportFileName="szerepkorok"
                    pageSize={10}
                    showExportButton={false}
                />
            </div>

            <AlertDialog open={!!deleteTarget} onOpenChange={(open) => !open && setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Biztosan törölni szeretnéd ezt a szerepkört?</AlertDialogTitle>
                        <p className="text-muted-foreground">{deleteTarget?.name}</p>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégsem</AlertDialogCancel>
                        <AlertDialogAction onClick={performDelete} className="bg-red-600 text-white hover:bg-red-700">
                            Törlés
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}
