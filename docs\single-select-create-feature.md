# SingleSelect Komponens - Új Elem Létrehozása Funkció

## Áttekintés

A SingleSelect komponens ki lett bővítve egy új funkcióval, amely lehetővé teszi új elemek dinamikus létrehozását közvetlenül a dropdown-ból. Ha a felhasználó begépel egy nem létező értéket, megjelenik egy "➕ Új hozzáadása: ___" opció, amelyre kattintva új elemet hozhat létre.

## Új Funkciók

### 1. 🆕 Dinamikus Elem Létrehozás
- **Automatikus észlelés**: Ha a keresett érték nem található a meglévő opciók között
- **Vizu<PERSON>lis jelz<PERSON>**: "➕ Új hozzáadása: [érték]" sor megjelenítése
- **Loading állapot**: Anim<PERSON>lt spinner a létrehozás során
- **Automatikus kiválasztás**: Sikeres létrehozás után az új elem automatikusan kiválasztódik

### 2. 🎛️ Új Props

```typescript
interface SingleSelectProps {
  // ... meglévő props
  onCreate?: (label: string) => Promise<Option>  // Új elem létrehozása
  createPrefix?: string                          // Prefix szöveg (default: "Új hozzáadása: ")
  allowCreate?: boolean                          // Engedélyezi-e az új elem létrehozást (default: true)
}
```

### 3. 🔧 Használat

#### Alapvető használat:
```tsx
<SingleSelect
  value={selectedValue}
  onChange={setSelectedValue}
  options={options}
  onCreate={async (label: string) => {
    const response = await fetch('/api/create-item', {
      method: 'POST',
      body: JSON.stringify({ name: label })
    });
    const newItem = await response.json();
    return {
      value: newItem.id,
      label: newItem.name
    };
  }}
  createPrefix="➕ Új hozzáadása: "
/>
```

#### Kapcsolattartók modulban:
```tsx
<SingleSelect
  value={data.org_unit_id}
  onChange={(value) => setData('org_unit_id', value)}
  options={orgUnits.map(unit => ({
    value: unit.id.toString(),
    label: unit.name
  }))}
  onCreate={async (name: string) => {
    const newUnit = await createOrgUnit(name);
    return {
      value: newUnit.id.toString(),
      label: newUnit.name
    };
  }}
  createPrefix="Új szervezeti egység: "
/>
```

## Implementáció Részletei

### Frontend Logika

#### 1. Új Elem Észlelése
```typescript
const canCreateNew = onCreate && 
  allowCreate && 
  searchTerm.trim() && 
  !filteredOptions.some(option => 
    option.label.toLowerCase() === searchTerm.toLowerCase()
  );
```

#### 2. Létrehozás Kezelése
```typescript
const handleCreateNew = async () => {
  if (!onCreate || !searchTerm.trim() || isCreating) return;
  
  setIsCreating(true);
  try {
    const newOption = await onCreate(searchTerm.trim());
    onChange(newOption.value);
    setSearchTerm('');
    setOpen(false);
  } catch (error) {
    console.error('Failed to create new option:', error);
  } finally {
    setIsCreating(false);
  }
};
```

#### 3. UI Megjelenítés
```tsx
{canCreateNew && (
  <CommandItem
    value={`create-new-${searchTerm}`}
    onSelect={handleCreateNew}
    className="cursor-pointer border-t border-border text-primary"
    disabled={isCreating}
  >
    <div className="mr-2 flex h-4 w-4 items-center justify-center">
      {isCreating ? (
        <Loader2 className="h-3 w-3 animate-spin" />
      ) : (
        <Plus className="h-3 w-3" />
      )}
    </div>
    <span className="truncate">
      {createPrefix}{searchTerm}
    </span>
  </CommandItem>
)}
```

### Backend API Végpontok

#### Szervezeti Egység Létrehozása
```php
// OrgUnitController.php
public function store(Request $request): JsonResponse
{
    $validated = $request->validate([
        'name' => 'required|string|max:255|unique:org_units,name',
    ]);

    $orgUnit = OrgUnit::create($validated);
    return response()->json($orgUnit, 201);
}
```

#### Garázs Létrehozása
```php
// GarageController.php
public function store(Request $request): JsonResponse
{
    $validated = $request->validate([
        'name' => 'required|string|max:255',
        'postal_code' => 'required|string|max:10',
        'city' => 'required|string|max:255',
        'address' => 'required|string|max:255',
    ]);

    $garage = Garage::create($validated);
    return response()->json($garage, 201);
}
```

#### Route-ok
```php
Route::post('admin/org-units', [OrgUnitController::class, 'store']);
Route::post('admin/garages', [GarageController::class, 'store']);
```

## Kapcsolattartók Modulban Való Használat

### ContactForm Komponens
```tsx
<SingleSelect
  value={data.org_unit_id || ''}
  onChange={(value) => setData('org_unit_id', value ? String(value) : '')}
  options={[
    { value: '', label: 'Nincs kiválasztva' },
    ...orgUnits.map((unit) => ({
      value: unit.id.toString(),
      label: unit.name,
    })),
  ]}
  onCreate={onCreateOrgUnit ? async (name: string) => {
    const newUnit = await onCreateOrgUnit(name);
    return {
      value: newUnit.id.toString(),
      label: newUnit.name,
    };
  } : undefined}
  createPrefix="Új szervezeti egység: "
/>
```

### Index Oldal Handler-ek
```tsx
const handleCreateOrgUnit = async (name: string): Promise<OrgUnit> => {
  const response = await fetch('/admin/org-units', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-TOKEN': getCsrfToken(),
    },
    body: JSON.stringify({ name }),
  });
  
  if (!response.ok) {
    throw new Error('Failed to create organization unit');
  }
  
  const newOrgUnit = await response.json();
  router.reload({ only: ['orgUnits'] }); // Refresh list
  return newOrgUnit;
};
```

## Tesztelés

### Teszt Oldal
- **URL**: `/test/single-select`
- **Funkciók**: Interaktív teszt az új elem létrehozás funkcióhoz
- **Használat**: Gépelj be új értéket és válaszd ki a "➕ Új hozzáadása: ..." opciót

### Tesztelési Lépések
1. Nyisd meg a select dropdown-ot
2. Gépelj be egy új értéket (pl. "Teszt Elem")
3. Megjelenik a "➕ Új hozzáadása: Teszt Elem" sor
4. Kattints rá
5. Loading állapot megjelenik
6. 1 másodperc múlva automatikusan kiválasztódik az új elem
7. Az új elem hozzáadódik a listához

## Előnyök

### 1. 🚀 Felhasználói Élmény
- **Gyors workflow**: Nem kell külön oldalra navigálni új elem létrehozásához
- **Kontextus megőrzése**: A form kitöltése közben maradhat a felhasználó
- **Azonnali visszajelzés**: Loading állapot és automatikus kiválasztás

### 2. 🔧 Fejlesztői Előnyök
- **Újrafelhasználható**: Bármely select komponenshez hozzáadható
- **Típusbiztos**: TypeScript támogatás
- **Flexibilis**: Testreszabható prefix és onCreate logika

### 3. 📊 Adatkezelés
- **Konzisztencia**: Automatikus lista frissítés
- **Validáció**: Backend validáció minden új elemnél
- **Hibakezelés**: Graceful error handling

## Jövőbeli Fejlesztési Lehetőségek

1. **Batch létrehozás**: Több elem egyszerre
2. **Részletes form**: Modal ablak részletesebb adatokkal
3. **Validáció**: Frontend validáció a létrehozás előtt
4. **Kategorizálás**: Új elemek automatikus kategorizálása
5. **Audit log**: Új elemek létrehozásának naplózása

## Kompatibilitás

- ✅ **React 18+**
- ✅ **TypeScript**
- ✅ **Inertia.js**
- ✅ **Laravel Backend**
- ✅ **ShadCN/UI komponensek**

A funkció teljes mértékben visszafelé kompatibilis - a meglévő SingleSelect használatok változtatás nélkül működnek tovább.
