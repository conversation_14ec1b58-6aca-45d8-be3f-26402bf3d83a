<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_provider_webhooks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('service_provider_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Webhook neve (pl. "Új javítási igény")
            $table->string('event_type'); // Esemény típusa (pl. "repair_request_created")
            $table->string('url'); // Webhook URL
            $table->string('method')->default('POST'); // HTTP metódus
            $table->json('headers')->nullable(); // Egyedi HTTP headerek
            $table->json('payload_template')->nullable(); // Payload sablon
            $table->string('secret')->nullable(); // Webhook secret
            $table->boolean('is_active')->default(true);
            $table->integer('timeout')->default(30); // Timeout másodpercben
            $table->integer('retry_attempts')->default(3); // Újrapróbálkozások száma
            $table->timestamp('last_called_at')->nullable();
            $table->string('last_status')->nullable(); // success, failed, timeout
            $table->text('last_response')->nullable();
            $table->timestamps();
            
            $table->index(['service_provider_id', 'event_type']);
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_provider_webhooks');
    }
};
